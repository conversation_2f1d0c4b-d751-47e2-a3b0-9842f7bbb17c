#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
混合方案：结合方案1和方案2的优势
================================================================================
策略：
1. 使用方案1的稳定架构作为基础
2. 加入方案2的集成学习思想
3. 针对弱势类别进行特化优化
4. 保持整体均衡性的同时提升总体准确率
================================================================================
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader, WeightedRandomSampler
import numpy as np
from pathlib import Path
import json
from collections import Counter
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, confusion_matrix
from tqdm import tqdm
import warnings
import matplotlib.pyplot as plt
import seaborn as sns
warnings.filterwarnings('ignore')

# 导入原有模块
from stable_training_v1 import StableFeatureExtractor, StableInfantCryDataset
from ensemble_training import MultiFeatureExtractor

class HybridFeatureExtractor:
    """混合特征提取器 - 基于方案1的稳定特征提取器，增加增强功能"""

    def __init__(self):
        self.stable_extractor = StableFeatureExtractor()

    def extract_features(self, audio_file):
        """提取混合特征 - 主要使用方案1的稳定特征"""
        try:
            # 使用方案1的稳定特征作为主要特征
            features = self.stable_extractor.extract_features(audio_file)
            return features

        except Exception as e:
            print(f"特征提取失败 {audio_file}: {e}")
            return None

class CategorySpecificNetwork(nn.Module):
    """类别特化网络"""
    
    def __init__(self, input_dim, hidden_dim=256):
        super(CategorySpecificNetwork, self).__init__()
        
        self.network = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.BatchNorm1d(hidden_dim),
            nn.ReLU(inplace=True),
            nn.Dropout(0.3),
            
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.BatchNorm1d(hidden_dim // 2),
            nn.ReLU(inplace=True),
            nn.Dropout(0.2),
            
            nn.Linear(hidden_dim // 2, 1)  # 输出单个类别的置信度
        )
        
    def forward(self, x):
        return self.network(x)

class HybridDeepInfantModel(nn.Module):
    """混合深度婴儿哭声模型 - 结合方案1稳定性和方案2集成思想"""
    
    def __init__(self, input_dim, num_classes=5, dropout=0.4):
        super(HybridDeepInfantModel, self).__init__()
        
        # 共享特征提取器（基于方案1的稳定架构）
        self.shared_feature_extractor = nn.Sequential(
            nn.Linear(input_dim, 1024),
            nn.BatchNorm1d(1024),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout),
            
            nn.Linear(1024, 512),
            nn.BatchNorm1d(512),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout),
        )
        
        # 类别特化网络（针对每个类别）
        self.category_networks = nn.ModuleList([
            CategorySpecificNetwork(512, 256) for _ in range(num_classes)
        ])
        
        # 集成融合层
        self.fusion_layer = nn.Sequential(
            nn.Linear(num_classes, 128),
            nn.BatchNorm1d(128),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout / 2),
            
            nn.Linear(128, num_classes)
        )
        
        # 全局分类器（方案1风格）
        self.global_classifier = nn.Sequential(
            nn.Linear(512, 256),
            nn.BatchNorm1d(256),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout / 2),
            
            nn.Linear(256, num_classes)
        )
        
        self._initialize_weights()
    
    def _initialize_weights(self):
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.BatchNorm1d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
    
    def forward(self, x):
        # 共享特征提取
        shared_features = self.shared_feature_extractor(x)
        
        # 类别特化预测
        category_outputs = []
        for category_net in self.category_networks:
            category_output = category_net(shared_features)
            category_outputs.append(category_output)
        
        # 拼接类别特化输出
        category_concat = torch.cat(category_outputs, dim=1)
        
        # 融合预测
        fusion_output = self.fusion_layer(category_concat)
        
        # 全局预测
        global_output = self.global_classifier(shared_features)
        
        # 加权融合：融合输出权重0.6，全局输出权重0.4
        final_output = 0.6 * fusion_output + 0.4 * global_output
        
        return final_output

class HybridInfantCryDataset(Dataset):
    """混合数据集 - 针对弱势类别进行数据增强"""
    
    def __init__(self, data_dir, feature_extractor, split='train', enhance_weak_classes=True):
        self.data_dir = Path(data_dir)
        self.feature_extractor = feature_extractor
        self.split = split
        self.enhance_weak_classes = enhance_weak_classes
        
        # 类别映射
        self.label_map = {
            'belly_pain': 0,
            'burping': 1, 
            'discomfort': 2,
            'hungry': 3,
            'tired': 4
        }
        
        self.samples = []
        self._load_data()
        
        if self.enhance_weak_classes and split == 'train':
            self._enhance_weak_classes()
    
    def _load_data(self):
        """加载数据"""
        for label_name, label_idx in self.label_map.items():
            label_dir = self.data_dir / label_name
            if label_dir.exists():
                audio_files = list(label_dir.glob('*.wav'))
                
                # 数据分割
                if len(audio_files) > 0:
                    train_files, test_files = train_test_split(
                        audio_files, test_size=0.2, random_state=42
                    )
                    
                    files_to_use = train_files if self.split == 'train' else test_files
                    
                    for audio_file in files_to_use:
                        self.samples.append((audio_file, label_idx, label_name))
        
        print(f"加载了 {len(self.samples)} 个{self.split}样本")
    
    def _enhance_weak_classes(self):
        """增强弱势类别数据"""
        # 统计各类别样本数
        class_counts = Counter([sample[1] for sample in self.samples])
        max_count = max(class_counts.values())
        
        # 弱势类别：不适(2)和疲倦(4)
        weak_classes = [2, 4]  # discomfort, tired
        
        enhanced_samples = []
        for sample in self.samples:
            enhanced_samples.append(sample)
            
            # 对弱势类别进行额外增强
            if sample[1] in weak_classes:
                # 为弱势类别添加更多副本
                multiplier = max_count // class_counts[sample[1]]
                for _ in range(min(multiplier - 1, 3)):  # 最多增强3倍
                    enhanced_samples.append(sample)
        
        self.samples = enhanced_samples
        print(f"弱势类别增强后样本数: {len(self.samples)}")
    
    def __len__(self):
        return len(self.samples)
    
    def __getitem__(self, idx):
        audio_file, label, label_name = self.samples[idx]
        
        # 提取特征
        features = self.feature_extractor.extract_features(audio_file)
        
        if features is None:
            # 如果特征提取失败，返回零向量
            features = np.zeros(1024)  # 默认特征维度
        
        return torch.FloatTensor(features), torch.LongTensor([label])[0]

class BalancedFocalLoss(nn.Module):
    """平衡焦点损失 - 针对类别不平衡和困难样本"""

    def __init__(self, alpha=None, gamma=2.0, reduction='mean'):
        super(BalancedFocalLoss, self).__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.reduction = reduction

    def forward(self, inputs, targets):
        ce_loss = nn.functional.cross_entropy(inputs, targets, reduction='none')
        pt = torch.exp(-ce_loss)

        # 计算焦点权重
        focal_weight = (1 - pt) ** self.gamma

        # 计算类别权重
        if self.alpha is not None:
            alpha_t = self.alpha[targets]
            focal_loss = alpha_t * focal_weight * ce_loss
        else:
            focal_loss = focal_weight * ce_loss

        if self.reduction == 'mean':
            return focal_loss.mean()
        elif self.reduction == 'sum':
            return focal_loss.sum()
        else:
            return focal_loss

def create_hybrid_weighted_sampler(dataset):
    """创建混合加权采样器"""
    labels = [sample[1] for sample in dataset.samples]
    class_counts = Counter(labels)

    # 计算类别权重，对弱势类别给予更高权重
    weights = []
    weak_classes = [2, 4]  # discomfort, tired

    for label in labels:
        if label in weak_classes:
            # 弱势类别权重加倍
            weight = 2.0 / class_counts[label]
        else:
            weight = 1.0 / class_counts[label]
        weights.append(weight)

    return WeightedRandomSampler(weights, len(weights), replacement=True)

def train_hybrid_model(model, train_loader, val_loader, num_epochs=100, device='cuda'):
    """训练混合模型"""

    # 计算类别权重
    all_labels = []
    for _, labels in train_loader:
        all_labels.extend(labels.cpu().numpy())

    class_counts = Counter(all_labels)
    total_samples = len(all_labels)

    # 为弱势类别设置更高权重
    class_weights = []
    weak_classes = [2, 4]  # discomfort, tired

    for i in range(5):
        base_weight = total_samples / (5 * class_counts[i])
        if i in weak_classes:
            # 弱势类别权重增强
            class_weights.append(base_weight * 1.5)
        else:
            class_weights.append(base_weight)

    class_weights = torch.FloatTensor(class_weights).to(device)

    # 损失函数
    criterion = BalancedFocalLoss(alpha=class_weights, gamma=2.0)

    # 优化器 - 使用较小的学习率确保稳定性
    optimizer = optim.AdamW(model.parameters(), lr=0.001, weight_decay=0.01)

    # 学习率调度器
    scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(optimizer, T_0=20, T_mult=2)

    # 训练历史
    history = {'train_loss': [], 'train_acc': [], 'val_loss': [], 'val_acc': []}

    best_val_acc = 0.0
    best_model_state = None
    patience_counter = 0
    early_stopping_patience = 30

    print(f"开始混合模型训练，设备: {device}")
    print(f"类别权重: {class_weights.cpu().numpy()}")

    for epoch in range(num_epochs):
        # 训练阶段
        model.train()
        train_loss = 0.0
        train_correct = 0
        train_total = 0

        train_pbar = tqdm(train_loader, desc=f'Epoch {epoch+1}/{num_epochs} [Train]')
        for features, labels in train_pbar:
            features, labels = features.to(device), labels.to(device)

            optimizer.zero_grad()
            outputs = model(features)
            loss = criterion(outputs, labels)
            loss.backward()

            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)

            optimizer.step()

            train_loss += loss.item()
            _, predicted = torch.max(outputs.data, 1)
            train_total += labels.size(0)
            train_correct += (predicted == labels).sum().item()

            train_pbar.set_postfix({
                'Loss': f'{loss.item():.4f}',
                'Acc': f'{100.*train_correct/train_total:.2f}%'
            })

        # 验证阶段
        model.eval()
        val_loss = 0.0
        val_correct = 0
        val_total = 0

        with torch.no_grad():
            val_pbar = tqdm(val_loader, desc=f'Epoch {epoch+1}/{num_epochs} [Val]')
            for features, labels in val_pbar:
                features, labels = features.to(device), labels.to(device)

                outputs = model(features)
                loss = criterion(outputs, labels)

                val_loss += loss.item()
                _, predicted = torch.max(outputs.data, 1)
                val_total += labels.size(0)
                val_correct += (predicted == labels).sum().item()

                val_pbar.set_postfix({
                    'Loss': f'{loss.item():.4f}',
                    'Acc': f'{100.*val_correct/val_total:.2f}%'
                })

        # 计算平均损失和准确率
        avg_train_loss = train_loss / len(train_loader)
        avg_train_acc = 100. * train_correct / train_total
        avg_val_loss = val_loss / len(val_loader)
        avg_val_acc = 100. * val_correct / val_total

        # 记录历史
        history['train_loss'].append(avg_train_loss)
        history['train_acc'].append(avg_train_acc)
        history['val_loss'].append(avg_val_loss)
        history['val_acc'].append(avg_val_acc)

        # 保存最佳模型
        if avg_val_acc > best_val_acc:
            best_val_acc = avg_val_acc
            best_model_state = model.state_dict().copy()
            patience_counter = 0
        else:
            patience_counter += 1

        # 学习率调度
        scheduler.step()

        print(f'Epoch {epoch+1}/{num_epochs}:')
        print(f'  Train Loss: {avg_train_loss:.4f}, Train Acc: {avg_train_acc:.2f}%')
        print(f'  Val Loss: {avg_val_loss:.4f}, Val Acc: {avg_val_acc:.2f}%')
        print(f'  Best Val Acc: {best_val_acc:.2f}%')
        print(f'  LR: {optimizer.param_groups[0]["lr"]:.6f}')
        print('-' * 50)

        # 早停检查
        if patience_counter >= early_stopping_patience:
            print(f"早停触发！验证准确率在 {early_stopping_patience} 个 epoch 内没有改善。")
            break

    # 加载最佳模型
    if best_model_state is not None:
        model.load_state_dict(best_model_state)

    return model, history

def plot_training_history(history, save_path):
    """绘制训练历史"""
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5))

    # 损失曲线
    ax1.plot(history['train_loss'], label='Train Loss', color='blue')
    ax1.plot(history['val_loss'], label='Val Loss', color='red')
    ax1.set_title('Training and Validation Loss')
    ax1.set_xlabel('Epoch')
    ax1.set_ylabel('Loss')
    ax1.legend()
    ax1.grid(True)

    # 准确率曲线
    ax2.plot(history['train_acc'], label='Train Acc', color='blue')
    ax2.plot(history['val_acc'], label='Val Acc', color='red')
    ax2.set_title('Training and Validation Accuracy')
    ax2.set_xlabel('Epoch')
    ax2.set_ylabel('Accuracy (%)')
    ax2.legend()
    ax2.grid(True)

    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()

def evaluate_hybrid_model(model, test_loader, device):
    """评估混合模型"""
    model.eval()

    all_predictions = []
    all_labels = []
    all_confidences = []

    class_correct = [0] * 5
    class_total = [0] * 5

    label_names = ['belly_pain', 'burping', 'discomfort', 'hungry', 'tired']

    with torch.no_grad():
        for features, labels in tqdm(test_loader, desc='评估中'):
            features, labels = features.to(device), labels.to(device)

            outputs = model(features)
            probabilities = torch.softmax(outputs, dim=1)
            confidences, predicted = torch.max(probabilities, 1)

            all_predictions.extend(predicted.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())
            all_confidences.extend(confidences.cpu().numpy())

            # 统计各类别准确率
            for i in range(labels.size(0)):
                label = labels[i].item()
                class_total[label] += 1
                if predicted[i] == label:
                    class_correct[label] += 1

    # 计算总体准确率
    total_correct = sum(class_correct)
    total_samples = sum(class_total)
    overall_accuracy = total_correct / total_samples if total_samples > 0 else 0

    # 计算各类别准确率
    class_accuracies = {}
    for i in range(5):
        if class_total[i] > 0:
            acc = class_correct[i] / class_total[i]
            class_accuracies[label_names[i]] = acc
        else:
            class_accuracies[label_names[i]] = 0

    return {
        'overall_accuracy': overall_accuracy,
        'class_accuracies': class_accuracies,
        'predictions': all_predictions,
        'labels': all_labels,
        'confidences': all_confidences,
        'total_samples': total_samples
    }

def main():
    """主函数"""
    print("🌟 混合方案：结合方案1和方案2的优势")
    print("=" * 80)
    print("策略：")
    print("1. 使用方案1的稳定架构作为基础")
    print("2. 加入方案2的集成学习思想")
    print("3. 针对弱势类别进行特化优化")
    print("4. 保持整体均衡性的同时提升总体准确率")
    print("=" * 80)

    # 设备配置
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")

    # 数据目录
    data_dir = Path("Data")
    output_dir = Path("hybrid_solution_results")
    output_dir.mkdir(exist_ok=True)

    # 创建混合特征提取器
    print("\n🔧 创建混合特征提取器...")
    feature_extractor = HybridFeatureExtractor()

    # 创建数据集
    print("\n📊 创建混合数据集...")
    train_dataset = HybridInfantCryDataset(
        data_dir, feature_extractor, split='train', enhance_weak_classes=True
    )
    val_dataset = HybridInfantCryDataset(
        data_dir, feature_extractor, split='test', enhance_weak_classes=False
    )

    if len(train_dataset) == 0:
        print("❌ 训练数据集为空")
        return

    # 获取特征维度
    sample_features, _ = train_dataset[0]
    input_dim = sample_features.shape[0]
    print(f"特征维度: {input_dim}")

    # 创建数据加载器
    train_sampler = create_hybrid_weighted_sampler(train_dataset)
    train_loader = DataLoader(train_dataset, batch_size=32, sampler=train_sampler)
    val_loader = DataLoader(val_dataset, batch_size=32, shuffle=False)

    # 创建混合模型
    print(f"\n🏗️ 创建混合模型...")
    model = HybridDeepInfantModel(input_dim=input_dim, num_classes=5)
    model = model.to(device)

    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")

    # 训练模型
    print("\n🎯 开始混合模型训练...")
    trained_model, history = train_hybrid_model(model, train_loader, val_loader, num_epochs=120, device=device)

    # 保存模型
    model_path = output_dir / "hybrid_model.pth"
    torch.save(trained_model.state_dict(), model_path)
    print(f"模型已保存: {model_path}")

    # 保存完整模型（包含架构）
    full_model_path = output_dir / "hybrid_full_model.pth"
    torch.save(trained_model, full_model_path)
    print(f"完整模型已保存: {full_model_path}")

    # 绘制训练历史
    history_plot_path = output_dir / "hybrid_training_history.png"
    plot_training_history(history, history_plot_path)
    print(f"训练历史图已保存: {history_plot_path}")

    # 评估模型
    print("\n📊 评估混合模型...")
    results = evaluate_hybrid_model(trained_model, val_loader, device)

    # 显示结果
    print(f"\n🎯 混合方案性能:")
    print(f"   总体准确率: {results['overall_accuracy']:.3f} ({results['overall_accuracy']*100:.1f}%)")
    print(f"   测试样本数: {results['total_samples']}")

    print(f"\n   各类别准确率:")
    class_names_cn = {
        'belly_pain': '腹痛',
        'burping': '打嗝',
        'discomfort': '不适',
        'hungry': '饥饿',
        'tired': '疲倦'
    }

    for class_name, accuracy in results['class_accuracies'].items():
        cn_name = class_names_cn[class_name]
        print(f"     {cn_name}: {accuracy:.3f} ({accuracy*100:.1f}%)")

    # 与现有方案对比
    print(f"\n📈 与现有方案对比:")
    print(f"   方案1准确率: 74.6%")
    print(f"   方案2准确率: 90.2%")
    print(f"   混合方案准确率: {results['overall_accuracy']*100:.1f}%")

    improvement_from_v1 = (results['overall_accuracy'] * 100) - 74.6
    improvement_from_v2 = (results['overall_accuracy'] * 100) - 90.2
    print(f"   相比方案1提升: {improvement_from_v1:+.1f} 个百分点")
    print(f"   相比方案2提升: {improvement_from_v2:+.1f} 个百分点")

    # 保存详细结果
    detailed_results = {
        'method': 'Hybrid Solution (V1+V2)',
        'overall_accuracy': results['overall_accuracy'],
        'total_samples': results['total_samples'],
        'class_accuracies': results['class_accuracies'],
        'improvement_over_v1': improvement_from_v1,
        'improvement_over_v2': improvement_from_v2,
        'training_history': history
    }

    results_path = output_dir / "hybrid_solution_results.json"
    with open(results_path, 'w', encoding='utf-8') as f:
        json.dump(detailed_results, f, indent=2, ensure_ascii=False)

    print(f"\n📋 详细结果已保存: {results_path}")
    print(f"\n✅ 混合方案训练完成！")

if __name__ == "__main__":
    main()
