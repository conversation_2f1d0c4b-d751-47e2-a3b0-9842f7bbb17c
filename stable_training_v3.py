#!/usr/bin/env python3
"""
方案3: 高级特征 + 注意力机制

这个版本使用高级音频特征和注意力机制，不依赖外部预训练模型。

作者: DeepInfant Team
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader, WeightedRandomSampler
import librosa
import numpy as np
from pathlib import Path
import json
from collections import Counter
from sklearn.model_selection import train_test_split
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

class AdvancedFeatureExtractor:
    """高级音频特征提取器"""
    
    def __init__(self):
        self.sr = 16000
        
    def extract_features(self, audio_path, target_length=16000*3):
        """提取高级音频特征"""
        try:
            # 加载音频
            audio, sr = librosa.load(audio_path, sr=self.sr)
            
            # 调整长度
            if len(audio) < target_length:
                audio = np.pad(audio, (0, target_length - len(audio)), mode='constant')
            else:
                audio = audio[:target_length]
            
            features = []
            
            # 1. 多尺度Mel频谱特征
            for n_mels in [64, 128, 256]:
                mel_spec = librosa.feature.melspectrogram(
                    y=audio, sr=sr, n_mels=n_mels, n_fft=1024, hop_length=512
                )
                mel_spec_db = librosa.power_to_db(mel_spec, ref=np.max)
                
                features.extend([
                    np.mean(mel_spec_db, axis=1),
                    np.std(mel_spec_db, axis=1),
                    np.max(mel_spec_db, axis=1),
                    np.min(mel_spec_db, axis=1),
                    np.median(mel_spec_db, axis=1),
                    np.percentile(mel_spec_db, 25, axis=1),
                    np.percentile(mel_spec_db, 75, axis=1)
                ])
            
            # 2. 多阶MFCC特征
            for n_mfcc in [13, 20, 26]:
                mfcc = librosa.feature.mfcc(y=audio, sr=sr, n_mfcc=n_mfcc)
                features.extend([
                    np.mean(mfcc, axis=1),
                    np.std(mfcc, axis=1),
                    np.max(mfcc, axis=1),
                    np.min(mfcc, axis=1)
                ])
                
                # MFCC一阶差分
                mfcc_delta = librosa.feature.delta(mfcc)
                features.extend([
                    np.mean(mfcc_delta, axis=1),
                    np.std(mfcc_delta, axis=1)
                ])
                
                # MFCC二阶差分
                mfcc_delta2 = librosa.feature.delta(mfcc, order=2)
                features.extend([
                    np.mean(mfcc_delta2, axis=1),
                    np.std(mfcc_delta2, axis=1)
                ])
            
            # 3. 色度特征
            chroma = librosa.feature.chroma_stft(y=audio, sr=sr)
            features.extend([
                np.mean(chroma, axis=1),
                np.std(chroma, axis=1),
                np.max(chroma, axis=1),
                np.min(chroma, axis=1)
            ])
            
            # 4. 谱特征
            spectral_centroids = librosa.feature.spectral_centroid(y=audio, sr=sr)
            spectral_bandwidth = librosa.feature.spectral_bandwidth(y=audio, sr=sr)
            spectral_rolloff = librosa.feature.spectral_rolloff(y=audio, sr=sr)
            spectral_contrast = librosa.feature.spectral_contrast(y=audio, sr=sr)
            spectral_flatness = librosa.feature.spectral_flatness(y=audio)
            
            features.extend([
                np.mean(spectral_centroids), np.std(spectral_centroids),
                np.mean(spectral_bandwidth), np.std(spectral_bandwidth),
                np.mean(spectral_rolloff), np.std(spectral_rolloff),
                np.mean(spectral_contrast), np.std(spectral_contrast),
                np.mean(spectral_flatness), np.std(spectral_flatness)
            ])
            
            # 5. 时域特征
            zcr = librosa.feature.zero_crossing_rate(audio)
            rms = librosa.feature.rms(y=audio)
            
            features.extend([
                np.mean(zcr), np.std(zcr), np.max(zcr), np.min(zcr),
                np.mean(rms), np.std(rms), np.max(rms), np.min(rms)
            ])
            
            # 6. 节奏特征
            tempo, beats = librosa.beat.beat_track(y=audio, sr=sr)
            features.append(tempo)
            
            # 7. 全局统计特征
            features.extend([
                np.mean(audio), np.std(audio), np.max(audio), np.min(audio),
                np.sum(audio**2), len(audio), np.var(audio), np.skew(audio), np.kurtosis(audio)
            ])
            
            # 安全地展平所有特征
            final_features = []
            for f in features:
                if isinstance(f, np.ndarray):
                    final_features.extend(f.flatten())
                elif isinstance(f, (int, float, np.integer, np.floating)):
                    final_features.append(float(f))
                else:
                    final_features.extend(np.array(f).flatten())
            
            return np.array(final_features, dtype=np.float32)
            
        except Exception as e:
            print(f"特征提取失败 {audio_path}: {e}")
            return np.zeros(2000, dtype=np.float32)

class AttentionModule(nn.Module):
    """注意力机制模块"""
    
    def __init__(self, input_dim, hidden_dim=256):
        super(AttentionModule, self).__init__()
        self.attention = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.Tanh(),
            nn.Linear(hidden_dim, 1),
            nn.Softmax(dim=1)
        )
        
    def forward(self, x):
        # x: [batch_size, seq_len, feature_dim]
        attention_weights = self.attention(x)  # [batch_size, seq_len, 1]
        attended_features = torch.sum(x * attention_weights, dim=1)  # [batch_size, feature_dim]
        return attended_features, attention_weights

class AdvancedDeepInfantModel(nn.Module):
    """高级DeepInfant模型 - 带注意力机制"""
    
    def __init__(self, input_dim, num_classes=5, dropout=0.4):
        super(AdvancedDeepInfantModel, self).__init__()
        
        # 特征映射层
        self.feature_mapping = nn.Sequential(
            nn.Linear(input_dim, 1024),
            nn.BatchNorm1d(1024),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout),
            
            nn.Linear(1024, 512),
            nn.BatchNorm1d(512),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout),
        )
        
        # 注意力机制
        self.attention = AttentionModule(512, 256)
        
        # 分类器
        self.classifier = nn.Sequential(
            nn.Linear(512, 256),
            nn.BatchNorm1d(256),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout),
            
            nn.Linear(256, 128),
            nn.BatchNorm1d(128),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout / 2),
            
            nn.Linear(128, 64),
            nn.BatchNorm1d(64),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout / 2),
            
            nn.Linear(64, num_classes)
        )
        
        self._initialize_weights()
    
    def _initialize_weights(self):
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.BatchNorm1d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
    
    def forward(self, x):
        # 特征映射
        features = self.feature_mapping(x)
        
        # 为注意力机制添加序列维度
        features = features.unsqueeze(1)  # [batch_size, 1, 512]
        
        # 注意力机制
        attended_features, attention_weights = self.attention(features)
        
        # 分类
        output = self.classifier(attended_features)
        return output

class AdvancedInfantCryDataset(Dataset):
    """高级婴儿哭声数据集"""
    
    def __init__(self, data_dir, feature_extractor, split='train', test_size=0.2):
        self.feature_extractor = feature_extractor
        self.label_map = {'bp': 0, 'bu': 1, 'dc': 2, 'hu': 3, 'ti': 4}
        
        # 收集数据
        self.data = self._collect_data(data_dir)
        
        # 划分数据集
        if len(self.data) > 0:
            train_data, test_data = train_test_split(
                self.data, test_size=test_size, random_state=42, 
                stratify=[item[1] for item in self.data]
            )
            
            if split == 'train':
                self.data = train_data
            else:
                self.data = test_data
        
        print(f"{split} 集大小: {len(self.data)}")
        if len(self.data) > 0:
            label_counts = Counter([item[1] for item in self.data])
            print(f"标签分布: {dict(label_counts)}")
    
    def _collect_data(self, data_dir):
        """收集数据"""
        data = []
        data_dir = Path(data_dir)
        
        dir_to_label = {
            'belly_pain': 'bp', 'burping': 'bu', 'discomfort': 'dc',
            'hungry': 'hu', 'tired': 'ti'
        }
        
        for dir_name, label in dir_to_label.items():
            dir_path = data_dir / dir_name
            if dir_path.exists():
                audio_files = list(dir_path.glob('*.wav'))
                for audio_file in audio_files:
                    data.append((str(audio_file), self.label_map[label]))
                print(f"从 {dir_name} 收集了 {len(audio_files)} 个文件")
        
        return data
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        audio_path, label = self.data[idx]
        features = self.feature_extractor.extract_features(audio_path)
        return torch.FloatTensor(features), torch.LongTensor([label])[0]

def create_balanced_sampler(dataset):
    """创建平衡采样器"""
    labels = [dataset[i][1].item() for i in range(len(dataset))]
    label_counts = Counter(labels)
    
    # 计算权重
    total_samples = len(labels)
    num_classes = len(label_counts)
    
    weights = []
    for label in labels:
        weight = total_samples / (num_classes * label_counts[label])
        weights.append(weight)
    
    return WeightedRandomSampler(weights, total_samples, replacement=True)

def train_advanced_model():
    """训练高级模型"""
    print("🚀 方案3: 高级特征 + 注意力机制训练")
    print("=" * 60)
    
    # 设备选择
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 创建输出目录
    output_dir = Path("advanced_training_v3_results")
    output_dir.mkdir(exist_ok=True)
    
    # 初始化特征提取器
    feature_extractor = AdvancedFeatureExtractor()
    
    # 创建数据集
    train_dataset = AdvancedInfantCryDataset('Data', feature_extractor, split='train')
    val_dataset = AdvancedInfantCryDataset('Data', feature_extractor, split='test')
    
    if len(train_dataset) == 0:
        print("❌ 数据集为空")
        return
    
    # 获取特征维度
    sample_features, _ = train_dataset[0]
    input_dim = sample_features.shape[0]
    print(f"特征维度: {input_dim}")
    
    # 创建数据加载器
    train_sampler = create_balanced_sampler(train_dataset)
    train_loader = DataLoader(train_dataset, batch_size=32, sampler=train_sampler)
    val_loader = DataLoader(val_dataset, batch_size=32, shuffle=False)
    
    # 创建模型
    model = AdvancedDeepInfantModel(input_dim=input_dim, num_classes=5)
    model = model.to(device)
    
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 训练配置
    criterion = nn.CrossEntropyLoss(label_smoothing=0.1)
    optimizer = optim.AdamW(model.parameters(), lr=0.001, weight_decay=0.01)
    scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=50)
    
    # 训练循环
    best_val_acc = 0.0
    history = {'train_loss': [], 'train_acc': [], 'val_loss': [], 'val_acc': []}
    patience_counter = 0
    
    for epoch in range(120):
        # 训练
        model.train()
        train_loss = 0.0
        train_correct = 0
        train_total = 0
        
        train_pbar = tqdm(train_loader, desc=f'Epoch {epoch+1} [Train]')
        for features, labels in train_pbar:
            features, labels = features.to(device), labels.to(device)
            
            optimizer.zero_grad()
            outputs = model(features)
            loss = criterion(outputs, labels)
            loss.backward()
            
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()
            
            train_loss += loss.item()
            _, predicted = torch.max(outputs.data, 1)
            train_total += labels.size(0)
            train_correct += (predicted == labels).sum().item()
            
            train_pbar.set_postfix({
                'Loss': f'{loss.item():.4f}',
                'Acc': f'{100.*train_correct/train_total:.2f}%'
            })
        
        # 验证
        model.eval()
        val_loss = 0.0
        val_correct = 0
        val_total = 0
        
        with torch.no_grad():
            val_pbar = tqdm(val_loader, desc=f'Epoch {epoch+1} [Val]')
            for features, labels in val_pbar:
                features, labels = features.to(device), labels.to(device)
                outputs = model(features)
                loss = criterion(outputs, labels)
                
                val_loss += loss.item()
                _, predicted = torch.max(outputs.data, 1)
                val_total += labels.size(0)
                val_correct += (predicted == labels).sum().item()
                
                val_pbar.set_postfix({
                    'Loss': f'{loss.item():.4f}',
                    'Acc': f'{100.*val_correct/val_total:.2f}%'
                })
        
        # 计算指标
        avg_train_loss = train_loss / len(train_loader)
        avg_train_acc = 100. * train_correct / train_total
        avg_val_loss = val_loss / len(val_loader)
        avg_val_acc = 100. * val_correct / val_total
        
        # 记录历史
        history['train_loss'].append(avg_train_loss)
        history['train_acc'].append(avg_train_acc)
        history['val_loss'].append(avg_val_loss)
        history['val_acc'].append(avg_val_acc)
        
        # 学习率调度
        scheduler.step()
        
        # 保存最佳模型
        if avg_val_acc > best_val_acc:
            best_val_acc = avg_val_acc
            torch.save(model.state_dict(), output_dir / "best_advanced_v3_model.pth")
            torch.save(model, output_dir / "advanced_v3_full_model.pth")
            patience_counter = 0
        else:
            patience_counter += 1
        
        print(f'Epoch {epoch+1}: Train Acc: {avg_train_acc:.2f}%, Val Acc: {avg_val_acc:.2f}%, Best: {best_val_acc:.2f}%')
        
        # 早停
        if patience_counter >= 25:
            print("早停触发")
            break
    
    # 保存结果
    with open(output_dir / "advanced_v3_results.json", 'w') as f:
        json.dump(history, f, indent=2)
    
    print(f"✅ 方案3训练完成！最佳验证准确率: {best_val_acc:.2f}%")
    return best_val_acc

if __name__ == "__main__":
    # 添加scipy.stats导入以支持skew和kurtosis
    try:
        from scipy.stats import skew, kurtosis
        np.skew = skew
        np.kurtosis = kurtosis
    except ImportError:
        # 如果没有scipy，使用简单的替代函数
        def simple_skew(x):
            return 0.0
        def simple_kurtosis(x):
            return 0.0
        np.skew = simple_skew
        np.kurtosis = simple_kurtosis
    
    train_advanced_model()
