#!/usr/bin/env python3
"""
安装 YAMNet 训练所需的依赖

这个脚本会安装训练过程中需要的所有依赖包。

作者: DeepInfant Team
"""

import subprocess
import sys
import importlib
from pathlib import Path

def check_package(package_name, import_name=None):
    """检查包是否已安装"""
    if import_name is None:
        import_name = package_name
    
    try:
        importlib.import_module(import_name)
        return True
    except ImportError:
        return False

def install_package(package_name):
    """安装包"""
    print(f"📦 安装 {package_name}...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
        print(f"✅ {package_name} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {package_name} 安装失败: {e}")
        return False

def main():
    print("🚀 YAMNet 训练依赖安装")
    print("=" * 50)
    
    # 定义所需的包
    required_packages = [
        # 基础机器学习包
        ("torch", "torch"),
        ("torchvision", "torchvision"),
        ("numpy", "numpy"),
        ("scikit-learn", "sklearn"),
        
        # 音频处理
        ("librosa", "librosa"),
        
        # TensorFlow 和 TensorFlow Hub (用于 YAMNet)
        ("tensorflow", "tensorflow"),
        ("tensorflow-hub", "tensorflow_hub"),
        
        # 可视化
        ("matplotlib", "matplotlib"),
        ("seaborn", "seaborn"),
        
        # 进度条
        ("tqdm", "tqdm"),
        
        # 其他工具
        ("pathlib", "pathlib"),  # Python 3.4+ 内置
    ]
    
    # 检查已安装的包
    print("🔍 检查已安装的包...")
    installed = []
    missing = []
    
    for package_name, import_name in required_packages:
        if import_name == "pathlib":  # 跳过内置包
            continue
            
        if check_package(package_name, import_name):
            print(f"✅ {package_name} 已安装")
            installed.append(package_name)
        else:
            print(f"❌ {package_name} 未安装")
            missing.append(package_name)
    
    if not missing:
        print("\n🎉 所有依赖都已安装！")
        return
    
    print(f"\n📋 需要安装 {len(missing)} 个包:")
    for package in missing:
        print(f"   - {package}")
    
    # 询问是否安装
    response = input("\n是否现在安装这些包？(y/n): ").lower().strip()
    if response not in ['y', 'yes', '是']:
        print("取消安装。")
        return
    
    # 特殊处理 TensorFlow
    if 'tensorflow' in missing:
        print("\n🔧 安装 TensorFlow...")
        # 根据系统选择合适的 TensorFlow 版本
        try:
            import platform
            if platform.system() == "Darwin" and platform.processor() == "arm":
                # Apple Silicon Mac
                tf_package = "tensorflow-macos"
            else:
                # 其他系统
                tf_package = "tensorflow"
            
            if install_package(tf_package):
                missing.remove('tensorflow')
        except Exception as e:
            print(f"TensorFlow 安装遇到问题: {e}")
    
    # 安装其他缺失的包
    print("\n🔧 安装其他依赖...")
    success_count = 0
    
    for package in missing:
        if package == 'tensorflow':  # 已经处理过
            continue
            
        if install_package(package):
            success_count += 1
    
    # 安装结果
    total_missing = len(missing)
    if 'tensorflow' in missing:
        success_count += 1  # 假设 TensorFlow 安装成功
    
    print(f"\n📊 安装结果:")
    print(f"✅ 成功: {success_count}/{total_missing}")
    
    if success_count == total_missing:
        print("🎉 所有依赖安装完成！")
        print("\n现在可以运行训练脚本:")
        print("python train_with_yamnet_backbone.py")
    else:
        print("⚠️ 部分依赖安装失败，请手动安装:")
        failed = [pkg for pkg in missing if not check_package(pkg)]
        for package in failed:
            print(f"pip install {package}")
    
    # 额外的安装建议
    print(f"\n💡 额外建议:")
    print("1. 如果 TensorFlow 安装失败，可以尝试:")
    print("   pip install tensorflow-cpu  # CPU 版本")
    print("   pip install tensorflow-gpu  # GPU 版本 (需要 CUDA)")
    
    print("\n2. 如果在 Apple Silicon Mac 上:")
    print("   pip install tensorflow-macos")
    print("   pip install tensorflow-metal  # 可选，用于 GPU 加速")
    
    print("\n3. 如果遇到版本冲突，可以创建虚拟环境:")
    print("   python -m venv yamnet_env")
    print("   source yamnet_env/bin/activate  # Linux/Mac")
    print("   yamnet_env\\Scripts\\activate     # Windows")

if __name__ == "__main__":
    main()
