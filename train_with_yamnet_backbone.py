#!/usr/bin/env python3
"""
使用 YAMNet 作为特征提取 backbone 训练精确架构模型

这个脚本实现了：
1. 使用预训练的 YAMNet 作为特征提取器
2. 在精确架构的基础上进行微调训练
3. 处理数据不平衡问题
4. 实现迁移学习策略

作者: DeepInfant Team
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader, WeightedRandomSampler
import torch.nn.functional as F
import librosa
import numpy as np
from pathlib import Path
import json
from collections import defaultdict, Counter
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, confusion_matrix
import matplotlib.pyplot as plt
import seaborn as sns
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

# 尝试导入 TensorFlow Hub 和 YAMNet
try:
    import tensorflow as tf
    import tensorflow_hub as hub
    YAMNET_AVAILABLE = True
    print("✅ TensorFlow 和 TensorFlow Hub 可用")
except ImportError:
    YAMNET_AVAILABLE = False
    print("⚠️ TensorFlow Hub 不可用，将使用替代方案")

class YAMNetFeatureExtractor:
    """YAMNet 特征提取器"""
    
    def __init__(self):
        if YAMNET_AVAILABLE:
            try:
                # 加载预训练的 YAMNet 模型
                self.yamnet = hub.load('https://tfhub.dev/google/yamnet/1')
                print("✅ YAMNet 模型加载成功")
                self.available = True
            except Exception as e:
                print(f"❌ YAMNet 加载失败: {e}")
                self.available = False
        else:
            self.available = False
    
    def extract_features(self, audio_path, target_length=16000*3):
        """提取 YAMNet 特征"""
        if not self.available:
            return self._extract_mel_features(audio_path, target_length)
        
        try:
            # 加载音频
            audio, sr = librosa.load(audio_path, sr=16000)
            
            # 调整长度
            if len(audio) < target_length:
                audio = np.pad(audio, (0, target_length - len(audio)), mode='constant')
            else:
                audio = audio[:target_length]
            
            # 转换为 TensorFlow 张量
            audio_tensor = tf.convert_to_tensor(audio, dtype=tf.float32)
            
            # 提取 YAMNet 特征
            _, embeddings, _ = self.yamnet(audio_tensor)
            
            # 转换为 numpy 并取平均
            features = embeddings.numpy().mean(axis=0)  # 1024 维特征
            
            return features.astype(np.float32)
            
        except Exception as e:
            print(f"YAMNet 特征提取失败: {e}，使用备用方案")
            return self._extract_mel_features(audio_path, target_length)
    
    def _extract_mel_features(self, audio_path, target_length):
        """备用的 mel 频谱特征提取"""
        try:
            # 加载音频
            audio, sr = librosa.load(audio_path, sr=16000)
            
            # 调整长度
            if len(audio) < target_length:
                audio = np.pad(audio, (0, target_length - len(audio)), mode='constant')
            else:
                audio = audio[:target_length]
            
            # 提取 mel 频谱特征
            mel_spec = librosa.feature.melspectrogram(
                y=audio, sr=sr, n_fft=1024, hop_length=512, n_mels=128
            )
            mel_spec = librosa.power_to_db(mel_spec, ref=np.max)
            
            # 统计特征
            features = np.concatenate([
                mel_spec.mean(axis=1),  # 128 维
                mel_spec.std(axis=1),   # 128 维
                mel_spec.max(axis=1),   # 128 维
                mel_spec.min(axis=1),   # 128 维
            ])
            
            return features.astype(np.float32)  # 512 维特征
            
        except Exception as e:
            print(f"备用特征提取也失败: {e}")
            # 返回零特征作为最后的备选
            return np.zeros(512, dtype=np.float32)

class InfantCryDataset(Dataset):
    """婴儿哭声数据集"""
    
    def __init__(self, data_dir, feature_extractor, split='train', test_size=0.2, random_state=42):
        self.feature_extractor = feature_extractor
        self.label_map = {
            'bp': 0, 'bu': 1, 'ch': 2, 'dc': 3, 'hu': 4,
            'lo': 5, 'sc': 6, 'ti': 7, 'un': 8
        }
        self.label_names = {
            0: 'belly_pain', 1: 'burping', 2: 'cold_hot', 3: 'discomfort', 4: 'hungry',
            5: 'lonely', 6: 'scared', 7: 'tired', 8: 'unknown'
        }
        
        # 收集数据
        self.data = self._collect_data(data_dir)
        
        # 划分训练/测试集
        if len(self.data) > 0:
            train_data, test_data = train_test_split(
                self.data, test_size=test_size, random_state=random_state, 
                stratify=[item[1] for item in self.data]
            )
            
            if split == 'train':
                self.data = train_data
            else:
                self.data = test_data
        
        print(f"{split} 集大小: {len(self.data)}")
        if len(self.data) > 0:
            label_counts = Counter([item[1] for item in self.data])
            print(f"标签分布: {dict(label_counts)}")
    
    def _collect_data(self, data_dir):
        """收集数据文件"""
        data = []
        data_dir = Path(data_dir)
        
        # 目录到标签的映射
        dir_to_label = {
            'belly_pain': 'bp',
            'burping': 'bu', 
            'discomfort': 'dc',
            'hungry': 'hu',
            'tired': 'ti'
        }
        
        for dir_name, label in dir_to_label.items():
            dir_path = data_dir / dir_name
            if dir_path.exists():
                audio_files = list(dir_path.glob('*.wav'))
                for audio_file in audio_files:
                    data.append((str(audio_file), self.label_map[label]))
                print(f"从 {dir_name} 收集了 {len(audio_files)} 个文件")
        
        return data
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        audio_path, label = self.data[idx]
        
        # 提取特征
        features = self.feature_extractor.extract_features(audio_path)
        
        return torch.FloatTensor(features), torch.LongTensor([label])[0]

class YAMNetDeepInfantModel(nn.Module):
    """基于 YAMNet 特征的 DeepInfant 模型"""
    
    def __init__(self, input_dim=1024, num_classes=9, dropout=0.5):
        super(YAMNetDeepInfantModel, self).__init__()
        
        # 特征维度适配
        if input_dim == 1024:  # YAMNet 特征
            hidden_dims = [512, 256, 128]
        else:  # 备用 mel 特征 (512)
            hidden_dims = [256, 128, 64]
        
        # 构建分类器
        layers = []
        prev_dim = input_dim
        
        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.BatchNorm1d(hidden_dim),
                nn.ReLU(inplace=True),
                nn.Dropout(dropout)
            ])
            prev_dim = hidden_dim
        
        # 最终分类层
        layers.append(nn.Linear(prev_dim, num_classes))
        
        self.classifier = nn.Sequential(*layers)
        
        # 权重初始化
        self._initialize_weights()
    
    def _initialize_weights(self):
        """初始化权重"""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.BatchNorm1d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
    
    def forward(self, x):
        return self.classifier(x)

def create_balanced_sampler(dataset):
    """创建平衡采样器"""
    labels = [dataset[i][1].item() for i in range(len(dataset))]
    label_counts = Counter(labels)
    
    # 计算权重
    total_samples = len(labels)
    num_classes = len(label_counts)
    
    weights = []
    for label in labels:
        weight = total_samples / (num_classes * label_counts[label])
        weights.append(weight)
    
    return WeightedRandomSampler(weights, total_samples, replacement=True)

def train_model(model, train_loader, val_loader, num_epochs=50, device='cpu'):
    """训练模型"""
    
    # 损失函数和优化器
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.AdamW(model.parameters(), lr=0.001, weight_decay=0.01)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', patience=5, factor=0.5)
    
    # 训练历史
    history = {
        'train_loss': [], 'train_acc': [],
        'val_loss': [], 'val_acc': []
    }
    
    best_val_acc = 0.0
    best_model_state = None
    
    print(f"开始训练，设备: {device}")
    
    for epoch in range(num_epochs):
        # 训练阶段
        model.train()
        train_loss = 0.0
        train_correct = 0
        train_total = 0
        
        train_pbar = tqdm(train_loader, desc=f'Epoch {epoch+1}/{num_epochs} [Train]')
        for features, labels in train_pbar:
            features, labels = features.to(device), labels.to(device)
            
            optimizer.zero_grad()
            outputs = model(features)
            loss = criterion(outputs, labels)
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item()
            _, predicted = torch.max(outputs.data, 1)
            train_total += labels.size(0)
            train_correct += (predicted == labels).sum().item()
            
            # 更新进度条
            train_pbar.set_postfix({
                'Loss': f'{loss.item():.4f}',
                'Acc': f'{100.*train_correct/train_total:.2f}%'
            })
        
        # 验证阶段
        model.eval()
        val_loss = 0.0
        val_correct = 0
        val_total = 0
        
        with torch.no_grad():
            val_pbar = tqdm(val_loader, desc=f'Epoch {epoch+1}/{num_epochs} [Val]')
            for features, labels in val_pbar:
                features, labels = features.to(device), labels.to(device)
                outputs = model(features)
                loss = criterion(outputs, labels)
                
                val_loss += loss.item()
                _, predicted = torch.max(outputs.data, 1)
                val_total += labels.size(0)
                val_correct += (predicted == labels).sum().item()
                
                val_pbar.set_postfix({
                    'Loss': f'{loss.item():.4f}',
                    'Acc': f'{100.*val_correct/val_total:.2f}%'
                })
        
        # 计算平均指标
        avg_train_loss = train_loss / len(train_loader)
        avg_train_acc = 100. * train_correct / train_total
        avg_val_loss = val_loss / len(val_loader)
        avg_val_acc = 100. * val_correct / val_total
        
        # 记录历史
        history['train_loss'].append(avg_train_loss)
        history['train_acc'].append(avg_train_acc)
        history['val_loss'].append(avg_val_loss)
        history['val_acc'].append(avg_val_acc)
        
        # 学习率调度
        scheduler.step(avg_val_loss)
        
        # 保存最佳模型
        if avg_val_acc > best_val_acc:
            best_val_acc = avg_val_acc
            best_model_state = model.state_dict().copy()
        
        print(f'Epoch {epoch+1}/{num_epochs}:')
        print(f'  Train Loss: {avg_train_loss:.4f}, Train Acc: {avg_train_acc:.2f}%')
        print(f'  Val Loss: {avg_val_loss:.4f}, Val Acc: {avg_val_acc:.2f}%')
        print(f'  Best Val Acc: {best_val_acc:.2f}%')
        print('-' * 50)
    
    # 加载最佳模型
    if best_model_state is not None:
        model.load_state_dict(best_model_state)
    
    return model, history

def evaluate_model(model, test_loader, device='cpu'):
    """评估模型"""
    model.eval()
    all_predictions = []
    all_labels = []
    
    with torch.no_grad():
        for features, labels in tqdm(test_loader, desc='Evaluating'):
            features, labels = features.to(device), labels.to(device)
            outputs = model(features)
            _, predicted = torch.max(outputs, 1)
            
            all_predictions.extend(predicted.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())
    
    return all_predictions, all_labels

def plot_training_history(history, save_path='training_history.png'):
    """绘制训练历史"""
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 4))
    
    # 损失曲线
    ax1.plot(history['train_loss'], label='Train Loss')
    ax1.plot(history['val_loss'], label='Val Loss')
    ax1.set_title('Training and Validation Loss')
    ax1.set_xlabel('Epoch')
    ax1.set_ylabel('Loss')
    ax1.legend()
    ax1.grid(True)
    
    # 准确率曲线
    ax2.plot(history['train_acc'], label='Train Acc')
    ax2.plot(history['val_acc'], label='Val Acc')
    ax2.set_title('Training and Validation Accuracy')
    ax2.set_xlabel('Epoch')
    ax2.set_ylabel('Accuracy (%)')
    ax2.legend()
    ax2.grid(True)
    
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.show()
    print(f"训练历史图已保存: {save_path}")

def main():
    print("🚀 使用 YAMNet backbone 训练 DeepInfant 模型")
    print("=" * 60)
    
    # 设备选择
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 创建输出目录
    output_dir = Path("yamnet_training_results")
    output_dir.mkdir(exist_ok=True)
    
    # 初始化特征提取器
    print("\n📥 初始化 YAMNet 特征提取器...")
    feature_extractor = YAMNetFeatureExtractor()
    
    # 确定特征维度
    if feature_extractor.available:
        input_dim = 1024  # YAMNet 特征维度
        print("✅ 使用 YAMNet 特征 (1024 维)")
    else:
        input_dim = 512   # 备用 mel 特征维度
        print("⚠️ 使用备用 mel 特征 (512 维)")
    
    # 创建数据集
    print("\n📊 创建数据集...")
    train_dataset = InfantCryDataset('Data', feature_extractor, split='train')
    val_dataset = InfantCryDataset('Data', feature_extractor, split='test')
    
    if len(train_dataset) == 0 or len(val_dataset) == 0:
        print("❌ 数据集为空，请检查数据目录")
        return
    
    # 创建数据加载器
    train_sampler = create_balanced_sampler(train_dataset)
    train_loader = DataLoader(train_dataset, batch_size=32, sampler=train_sampler)
    val_loader = DataLoader(val_dataset, batch_size=32, shuffle=False)
    
    # 创建模型
    print(f"\n🏗️ 创建模型 (输入维度: {input_dim})...")
    model = YAMNetDeepInfantModel(input_dim=input_dim, num_classes=5)  # 只有5个类别
    model = model.to(device)
    
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 训练模型
    print("\n🎯 开始训练...")
    trained_model, history = train_model(model, train_loader, val_loader, num_epochs=50, device=device)
    
    # 保存模型
    model_path = output_dir / "yamnet_deepinfant_model.pth"
    torch.save(trained_model.state_dict(), model_path)
    print(f"✅ 模型已保存: {model_path}")
    
    # 保存完整模型
    full_model_path = output_dir / "yamnet_deepinfant_full_model.pth"
    torch.save(trained_model, full_model_path)
    print(f"✅ 完整模型已保存: {full_model_path}")
    
    # 绘制训练历史
    plot_training_history(history, output_dir / "training_history.png")
    
    # 评估模型
    print("\n📊 评估模型...")
    predictions, true_labels = evaluate_model(trained_model, val_loader, device)
    
    # 分类报告
    label_names = ['belly_pain', 'burping', 'discomfort', 'hungry', 'tired']
    report = classification_report(true_labels, predictions, target_names=label_names, output_dict=True)
    
    print("\n📋 分类报告:")
    print(classification_report(true_labels, predictions, target_names=label_names))
    
    # 保存结果
    results = {
        'training_history': history,
        'classification_report': report,
        'model_config': {
            'input_dim': input_dim,
            'num_classes': 5,
            'feature_extractor': 'YAMNet' if feature_extractor.available else 'Mel',
            'device': str(device)
        }
    }
    
    results_path = output_dir / "training_results.json"
    with open(results_path, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"✅ 训练结果已保存: {results_path}")
    print(f"📁 所有输出文件保存在: {output_dir}")

if __name__ == "__main__":
    main()
