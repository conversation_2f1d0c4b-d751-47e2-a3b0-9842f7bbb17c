#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试改进的方案1 - 评估性能提升
================================================================================
对比改进前后的性能表现，特别关注弱势类别的提升
================================================================================
"""

import torch
import torch.nn as nn
from torch.utils.data import DataLoader
import numpy as np
from pathlib import Path
import json
from collections import Counter, defaultdict
from tqdm import tqdm
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import classification_report, confusion_matrix
import time

# 导入改进的模型和数据集
from improve_solution1_plan import ImprovedFeatureExtractor, ImprovedDeepInfantModel
from improved_solution1_training import ImprovedInfantCryDataset

def evaluate_model(model, test_loader, device='cuda'):
    """评估模型性能"""
    model.eval()
    
    all_predictions = []
    all_labels = []
    all_confidences = []
    inference_times = []
    
    class_correct = [0] * 5
    class_total = [0] * 5
    
    label_names = ['belly_pain', 'burping', 'discomfort', 'hungry', 'tired']
    
    with torch.no_grad():
        for features, labels in tqdm(test_loader, desc="评估中"):
            features, labels = features.to(device), labels.to(device)
            
            # 测量推理时间
            start_time = time.time()
            outputs = model(features)
            inference_time = time.time() - start_time
            inference_times.append(inference_time / features.size(0))  # 每个样本的平均时间
            
            # 获取预测和置信度
            probabilities = torch.softmax(outputs, dim=1)
            confidences, predicted = torch.max(probabilities, 1)
            
            # 收集结果
            all_predictions.extend(predicted.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())
            all_confidences.extend(confidences.cpu().numpy())
            
            # 统计各类别准确率
            for i in range(labels.size(0)):
                label = labels[i].item()
                class_total[label] += 1
                if predicted[i] == labels[i]:
                    class_correct[label] += 1
    
    # 计算总体准确率
    total_correct = sum(class_correct)
    total_samples = sum(class_total)
    overall_accuracy = total_correct / total_samples if total_samples > 0 else 0
    
    # 计算各类别准确率
    class_accuracies = {}
    for i in range(5):
        if class_total[i] > 0:
            acc = class_correct[i] / class_total[i]
            class_accuracies[label_names[i]] = acc
        else:
            class_accuracies[label_names[i]] = 0
    
    # 计算平均推理时间
    avg_inference_time = np.mean(inference_times)
    
    return {
        'overall_accuracy': overall_accuracy,
        'class_accuracies': class_accuracies,
        'predictions': all_predictions,
        'labels': all_labels,
        'confidences': all_confidences,
        'avg_inference_time': avg_inference_time,
        'total_samples': total_samples
    }

def compare_with_baseline(improved_results, baseline_file="comprehensive_performance_report.json"):
    """与基线方案1对比"""
    try:
        with open(baseline_file, 'r', encoding='utf-8') as f:
            baseline_data = json.load(f)
        
        baseline_results = baseline_data['method_results']['方案1 (GPU稳定训练)']
        baseline_accuracy = baseline_results['overall_accuracy']
        baseline_class_accs = {}
        
        for class_name, class_data in baseline_results['dir_accuracies'].items():
            baseline_class_accs[class_name] = class_data['accuracy']
        
        print("📊 性能对比分析")
        print("=" * 80)
        print(f"总体准确率对比:")
        print(f"  原方案1: {baseline_accuracy:.1%}")
        print(f"  改进方案1: {improved_results['overall_accuracy']:.1%}")
        print(f"  提升: {(improved_results['overall_accuracy'] - baseline_accuracy):.1%}")
        
        print(f"\n各类别准确率对比:")
        class_mapping = {
            'belly_pain': 'belly_pain',
            'burping': 'burping', 
            'discomfort': 'discomfort',
            'hungry': 'hungry',
            'tired': 'tired'
        }
        
        improvements = {}
        for improved_class, baseline_class in class_mapping.items():
            if baseline_class in baseline_class_accs and improved_class in improved_results['class_accuracies']:
                baseline_acc = baseline_class_accs[baseline_class]
                improved_acc = improved_results['class_accuracies'][improved_class]
                improvement = improved_acc - baseline_acc
                improvements[improved_class] = improvement
                
                print(f"  {improved_class}:")
                print(f"    原方案1: {baseline_acc:.1%}")
                print(f"    改进方案1: {improved_acc:.1%}")
                print(f"    提升: {improvement:+.1%}")
        
        return improvements
        
    except FileNotFoundError:
        print("⚠️ 未找到基线结果文件，无法进行对比")
        return {}

def generate_detailed_report(results, improvements=None):
    """生成详细报告"""
    report = {
        "method": "Improved Solution 1",
        "overall_accuracy": results['overall_accuracy'],
        "total_samples": results['total_samples'],
        "avg_inference_time": results['avg_inference_time'],
        "class_accuracies": results['class_accuracies'],
        "improvements": improvements or {}
    }
    
    # 生成分类报告
    class_names = ['belly_pain', 'burping', 'discomfort', 'hungry', 'tired']
    clf_report = classification_report(
        results['labels'], 
        results['predictions'], 
        target_names=class_names,
        output_dict=True
    )
    
    report['classification_report'] = clf_report
    
    # 生成混淆矩阵
    cm = confusion_matrix(results['labels'], results['predictions'])
    report['confusion_matrix'] = cm.tolist()
    
    return report

def plot_comparison(improved_results, improvements=None):
    """绘制对比图表"""
    class_names = ['腹痛', '打嗝', '不适', '饥饿', '疲倦']
    class_keys = ['belly_pain', 'burping', 'discomfort', 'hungry', 'tired']
    
    improved_accs = [improved_results['class_accuracies'][key] for key in class_keys]
    
    # 如果有改进数据，也绘制基线
    if improvements:
        baseline_accs = [improved_accs[i] - improvements.get(class_keys[i], 0) for i in range(5)]
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # 准确率对比
        x = np.arange(len(class_names))
        width = 0.35
        
        ax1.bar(x - width/2, [acc*100 for acc in baseline_accs], width, label='原方案1', alpha=0.8)
        ax1.bar(x + width/2, [acc*100 for acc in improved_accs], width, label='改进方案1', alpha=0.8)
        
        ax1.set_xlabel('类别')
        ax1.set_ylabel('准确率 (%)')
        ax1.set_title('各类别准确率对比')
        ax1.set_xticks(x)
        ax1.set_xticklabels(class_names)
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 改进幅度
        improvement_values = [improvements.get(class_keys[i], 0)*100 for i in range(5)]
        colors = ['green' if x > 0 else 'red' for x in improvement_values]
        
        ax2.bar(class_names, improvement_values, color=colors, alpha=0.7)
        ax2.set_xlabel('类别')
        ax2.set_ylabel('改进幅度 (%)')
        ax2.set_title('各类别改进幅度')
        ax2.grid(True, alpha=0.3)
        ax2.axhline(y=0, color='black', linestyle='-', alpha=0.5)
        
        plt.tight_layout()
        plt.savefig('improved_solution1_comparison.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    else:
        # 只绘制改进结果
        plt.figure(figsize=(10, 6))
        bars = plt.bar(class_names, [acc*100 for acc in improved_accs], alpha=0.8)
        
        # 为弱势类别标记颜色
        bars[2].set_color('orange')  # 不适
        bars[4].set_color('orange')  # 疲倦
        
        plt.xlabel('类别')
        plt.ylabel('准确率 (%)')
        plt.title('改进方案1各类别准确率')
        plt.grid(True, alpha=0.3)
        
        # 添加数值标签
        for i, bar in enumerate(bars):
            height = bar.get_height()
            plt.text(bar.get_x() + bar.get_width()/2., height + 1,
                    f'{height:.1f}%', ha='center', va='bottom')
        
        plt.tight_layout()
        plt.savefig('improved_solution1_results.png', dpi=300, bbox_inches='tight')
        plt.show()

def main():
    """主函数"""
    print("🧪 测试改进的方案1")
    print("=" * 80)
    
    # 设备配置
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 检查模型文件
    model_path = Path("improved_solution1_results/improved_solution1_model.pth")
    if not model_path.exists():
        print("❌ 未找到改进的模型文件，请先运行训练脚本")
        return
    
    # 数据目录
    data_dir = Path("Data")
    
    # 创建特征提取器
    print("\n🔧 创建特征提取器...")
    feature_extractor = ImprovedFeatureExtractor()
    
    # 创建测试数据集
    print("\n📊 创建测试数据集...")
    test_dataset = ImprovedInfantCryDataset(
        data_dir, feature_extractor, split='test', augment_weak_classes=False
    )
    
    test_loader = DataLoader(test_dataset, batch_size=32, shuffle=False)
    
    # 加载模型
    print("\n🏗️ 加载改进的模型...")
    sample_features, _ = test_dataset[0]
    input_dim = sample_features.shape[0]
    
    model = ImprovedDeepInfantModel(input_dim=input_dim, num_classes=5)
    model.load_state_dict(torch.load(model_path, map_location=device))
    model = model.to(device)
    
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 评估模型
    print("\n🎯 评估改进的模型...")
    results = evaluate_model(model, test_loader, device)
    
    # 与基线对比
    print("\n📈 与原方案1对比...")
    improvements = compare_with_baseline(results)
    
    # 生成详细报告
    print("\n📋 生成详细报告...")
    detailed_report = generate_detailed_report(results, improvements)
    
    # 保存结果
    output_dir = Path("improved_solution1_results")
    with open(output_dir / "improved_solution1_evaluation.json", 'w', encoding='utf-8') as f:
        json.dump(detailed_report, f, indent=2, ensure_ascii=False)
    
    # 绘制对比图表
    print("\n📊 绘制对比图表...")
    plot_comparison(results, improvements)
    
    # 总结
    print("\n🎉 改进效果总结:")
    print(f"  总体准确率: {results['overall_accuracy']:.1%}")
    print(f"  平均推理时间: {results['avg_inference_time']:.4f}s")
    
    weak_classes = ['discomfort', 'tired']
    print(f"  弱势类别改进:")
    for class_name in weak_classes:
        acc = results['class_accuracies'][class_name]
        improvement = improvements.get(class_name, 0)
        print(f"    {class_name}: {acc:.1%} ({improvement:+.1%})")
    
    print(f"\n✅ 评估完成！结果已保存到 {output_dir}")

if __name__ == "__main__":
    main()
