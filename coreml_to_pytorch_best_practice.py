#!/usr/bin/env python3
"""
Core ML 到 PyTorch 最佳实践转换工具

这个脚本实现了推荐的转换管道：
Core ML → ONNX → PyTorch

特点：
1. 保留完整的权重信息
2. 自动数值对齐验证
3. 支持模型简化优化
4. 详细的转换报告

依赖安装：
pip install coremltools onnx onnx-simplifier onnx2pytorch torch

作者: DeepInfant Team
"""

import sys
import warnings
from pathlib import Path
import json
import numpy as np

# 检查必要的依赖
missing_deps = []

try:
    import coremltools as ct
except ImportError:
    missing_deps.append("coremltools")

try:
    import torch
except ImportError:
    missing_deps.append("torch")

try:
    import onnx
except ImportError:
    missing_deps.append("onnx")

try:
    from onnxsim import simplify
    ONNX_SIMPLIFIER_AVAILABLE = True
except ImportError:
    ONNX_SIMPLIFIER_AVAILABLE = False
    missing_deps.append("onnx-simplifier")

try:
    from onnx2pytorch import ConvertModel
    ONNX2PYTORCH_AVAILABLE = True
except ImportError:
    ONNX2PYTORCH_AVAILABLE = False
    missing_deps.append("onnx2pytorch")

if missing_deps:
    print("❌ 缺少必要的依赖包:")
    for dep in missing_deps:
        print(f"   - {dep}")
    print("\n请运行以下命令安装:")
    print(f"pip install {' '.join(missing_deps)}")
    sys.exit(1)

def convert_coreml_to_pytorch_best_practice(coreml_path, output_dir="./converted_models"):
    """
    使用最佳实践将 Core ML 模型转换为 PyTorch
    
    转换流程：
    1. Core ML → ONNX (使用 coremltools)
    2. ONNX 简化 (使用 onnx-simplifier)
    3. ONNX → PyTorch (使用 onnx2pytorch)
    4. 数值对齐验证
    
    Args:
        coreml_path: Core ML 模型路径
        output_dir: 输出目录
        
    Returns:
        dict: 转换结果
    """
    print(f"\n🚀 开始最佳实践转换: {coreml_path}")
    print("=" * 60)
    
    coreml_path = Path(coreml_path)
    output_dir = Path(output_dir)
    output_dir.mkdir(exist_ok=True)
    model_name = coreml_path.stem
    
    result = {
        'model_name': model_name,
        'coreml_path': str(coreml_path),
        'status': 'started',
        'steps': {}
    }
    
    try:
        # 步骤1: 载入 Core ML 模型
        print("📥 步骤1: 载入 Core ML 模型...")
        mlmodel = ct.models.MLModel(str(coreml_path))
        spec = mlmodel.get_spec()
        
        # 分析输入形状
        input_desc = spec.description.input[0]
        input_name = input_desc.name
        
        if input_desc.type.HasField('multiArrayType'):
            input_shape = list(input_desc.type.multiArrayType.shape)
            print(f"   输入名称: {input_name}")
            print(f"   输入形状: {input_shape}")
        else:
            # 默认音频输入形状
            input_shape = [1, 80, 432]  # mel频谱图格式
            print(f"   使用默认输入形状: {input_shape}")
        
        result['steps']['load_coreml'] = {
            'status': 'success',
            'input_name': input_name,
            'input_shape': input_shape
        }
        
        # 步骤2: Core ML → ONNX 转换
        print("\n🔄 步骤2: Core ML → ONNX 转换...")
        onnx_path = output_dir / f"{model_name}_from_coreml.onnx"
        
        # 创建输入类型描述
        if len(input_shape) == 1:
            # 1D 输入，转换为适合的形状 (假设是音频)
            tensor_shape = (1, 1, 80, 432)  # batch, channel, height, width
        elif len(input_shape) == 3:
            # 3D 输入，添加 batch 维度
            tensor_shape = (1, *input_shape)
        else:
            # 其他情况，直接使用
            tensor_shape = tuple(input_shape)
        
        tensor_input = ct.TensorType(shape=tensor_shape)
        print(f"   张量输入形状: {tensor_shape}")
        
        # 执行转换
        onnx_model = ct.converters.convert(
            mlmodel,
            convert_to="onnx",
            minimum_deployment_target=ct.target.iOS15,
            inputs=[tensor_input]
        )
        
        # 保存 ONNX 模型
        onnx.save(onnx_model, str(onnx_path))
        print(f"   ✅ ONNX 模型已保存: {onnx_path}")
        
        result['steps']['coreml_to_onnx'] = {
            'status': 'success',
            'onnx_path': str(onnx_path),
            'tensor_shape': tensor_shape
        }
        
        # 步骤3: ONNX 简化（可选但推荐）
        print("\n🔧 步骤3: ONNX 模型简化...")
        if ONNX_SIMPLIFIER_AVAILABLE:
            try:
                model_simp, check = simplify(str(onnx_path))
                if check:
                    simplified_path = output_dir / f"{model_name}_simplified.onnx"
                    onnx.save(model_simp, str(simplified_path))
                    print(f"   ✅ 简化模型已保存: {simplified_path}")
                    onnx_path = simplified_path  # 使用简化后的模型
                    result['steps']['onnx_simplify'] = {
                        'status': 'success',
                        'simplified_path': str(simplified_path)
                    }
                else:
                    print("   ⚠️ 模型简化失败，使用原始 ONNX 模型")
                    result['steps']['onnx_simplify'] = {'status': 'failed', 'reason': 'simplify_check_failed'}
            except Exception as e:
                print(f"   ⚠️ 模型简化出错: {e}，使用原始 ONNX 模型")
                result['steps']['onnx_simplify'] = {'status': 'error', 'error': str(e)}
        else:
            print("   ⚠️ onnx-simplifier 未安装，跳过简化步骤")
            result['steps']['onnx_simplify'] = {'status': 'skipped', 'reason': 'not_installed'}
        
        # 步骤4: ONNX → PyTorch 转换
        print("\n🔄 步骤4: ONNX → PyTorch 转换...")
        if ONNX2PYTORCH_AVAILABLE:
            try:
                pytorch_model = ConvertModel(str(onnx_path), experimental=True)
                print("   ✅ PyTorch 模型转换成功")
                
                result['steps']['onnx_to_pytorch'] = {
                    'status': 'success',
                    'model_created': True
                }
                
                # 步骤5: 数值对齐验证
                print("\n🧪 步骤5: 数值对齐验证...")
                validation_result = validate_conversion(mlmodel, pytorch_model, tensor_shape, input_name)
                result['steps']['validation'] = validation_result
                
                if validation_result['status'] == 'success':
                    # 保存 PyTorch 模型
                    pytorch_path = output_dir / f"{model_name}_converted.pth"
                    torch.save(pytorch_model.state_dict(), pytorch_path)
                    print(f"   ✅ PyTorch 模型已保存: {pytorch_path}")
                    
                    # 保存完整模型（包含架构）
                    full_model_path = output_dir / f"{model_name}_full_model.pth"
                    torch.save(pytorch_model, full_model_path)
                    print(f"   ✅ 完整模型已保存: {full_model_path}")
                    
                    result.update({
                        'status': 'success',
                        'pytorch_path': str(pytorch_path),
                        'full_model_path': str(full_model_path),
                        'final_onnx_path': str(onnx_path),
                        'validation': validation_result
                    })
                else:
                    print("   ❌ 数值对齐验证失败")
                    result['status'] = 'validation_failed'
                    
            except Exception as e:
                print(f"   ❌ ONNX → PyTorch 转换失败: {e}")
                result['steps']['onnx_to_pytorch'] = {
                    'status': 'error',
                    'error': str(e)
                }
                result['status'] = 'pytorch_conversion_failed'
        else:
            print("   ⚠️ onnx2pytorch 未安装，跳过 PyTorch 转换")
            result['steps']['onnx_to_pytorch'] = {
                'status': 'skipped',
                'reason': 'not_installed'
            }
            result.update({
                'status': 'onnx_only',
                'final_onnx_path': str(onnx_path),
                'message': '请安装 onnx2pytorch 以完成 PyTorch 转换'
            })
            
    except Exception as e:
        print(f"❌ 转换失败: {e}")
        result.update({
            'status': 'failed',
            'error': str(e)
        })
    
    return result

def validate_conversion(coreml_model, pytorch_model, input_shape, input_name):
    """验证转换后的模型输出是否与原始 Core ML 模型一致"""
    try:
        print("   🔍 创建测试输入...")
        test_input = np.random.randn(*input_shape).astype(np.float32)
        
        # Core ML 推理
        print("   🍎 Core ML 推理...")
        coreml_input = {input_name: test_input}
        coreml_output = coreml_model.predict(coreml_input)
        coreml_result = list(coreml_output.values())[0]
        
        # PyTorch 推理
        print("   🔥 PyTorch 推理...")
        pytorch_input = torch.from_numpy(test_input)
        with torch.no_grad():
            pytorch_result = pytorch_model(pytorch_input).numpy()
        
        # 比较结果
        print("   📊 比较输出结果...")
        if coreml_result.shape != pytorch_result.shape:
            return {
                'status': 'failed',
                'reason': 'shape_mismatch',
                'coreml_shape': list(coreml_result.shape),
                'pytorch_shape': list(pytorch_result.shape)
            }
        
        # 计算数值差异
        max_diff = np.max(np.abs(coreml_result - pytorch_result))
        mean_diff = np.mean(np.abs(coreml_result - pytorch_result))
        relative_diff = max_diff / (np.max(np.abs(coreml_result)) + 1e-8)
        
        print(f"      最大绝对差异: {max_diff:.6f}")
        print(f"      平均绝对差异: {mean_diff:.6f}")
        print(f"      相对差异: {relative_diff:.6f}")
        
        # 判断对齐质量
        if max_diff < 1e-5:
            quality = "excellent"
            print("   ✅ 数值对齐优秀 (< 1e-5)")
        elif max_diff < 1e-4:
            quality = "good"
            print("   ✅ 数值对齐良好 (< 1e-4)")
        elif max_diff < 1e-3:
            quality = "acceptable"
            print("   ⚠️ 数值对齐可接受 (< 1e-3)")
        else:
            quality = "poor"
            print("   ❌ 数值对齐较差 (>= 1e-3)")
        
        return {
            'status': 'success',
            'quality': quality,
            'max_diff': float(max_diff),
            'mean_diff': float(mean_diff),
            'relative_diff': float(relative_diff),
            'coreml_shape': list(coreml_result.shape),
            'pytorch_shape': list(pytorch_result.shape)
        }
        
    except Exception as e:
        print(f"   ❌ 验证过程出错: {e}")
        return {
            'status': 'error',
            'error': str(e)
        }

def main():
    print("🚀 Core ML 到 PyTorch 最佳实践转换工具")
    print("=" * 60)
    print("转换管道: Core ML → ONNX → PyTorch")
    print("特点: 保留权重 + 数值对齐验证")
    print("=" * 60)
    
    # 要转换的 Core ML 模型
    models_to_convert = [
        "Models/DeepInfant_VGGish.mlmodel",
        "Models/DeepInfant_AFP.mlmodel",
        "iOS_iPadOS/deepinfant/model/DeepInfant_VGGish.mlmodel",
        "iOS_iPadOS/deepinfant/model/DeepInfant_AFP.mlmodel"
    ]
    
    # 找到存在的模型文件
    existing_models = [model for model in models_to_convert if Path(model).exists()]
    
    if not existing_models:
        print("❌ 未找到任何 Core ML 模型文件")
        print("请确保以下路径中至少有一个模型文件:")
        for model in models_to_convert:
            print(f"   - {model}")
        return
    
    print(f"📁 找到 {len(existing_models)} 个模型文件:")
    for model in existing_models:
        print(f"   - {model}")
    
    # 创建输出目录
    output_dir = Path("converted_models_best_practice")
    output_dir.mkdir(exist_ok=True)
    print(f"\n📁 输出目录: {output_dir}")
    
    # 转换所有模型
    all_results = []
    
    for model_path in existing_models:
        result = convert_coreml_to_pytorch_best_practice(model_path, output_dir)
        all_results.append(result)
    
    # 保存详细报告
    report_path = output_dir / "conversion_report_best_practice.json"
    with open(report_path, 'w', encoding='utf-8') as f:
        json.dump(all_results, f, indent=2, ensure_ascii=False)
    
    # 显示总结
    print(f"\n{'='*60}")
    print("📊 转换总结")
    print("=" * 60)
    
    success_count = 0
    for result in all_results:
        model_name = result['model_name']
        status = result['status']
        
        if status == 'success':
            validation = result.get('validation', {})
            quality = validation.get('quality', 'unknown')
            print(f"✅ {model_name}: 转换成功 (数值对齐: {quality})")
            success_count += 1
        elif status == 'onnx_only':
            print(f"⚠️ {model_name}: 仅ONNX转换成功")
        else:
            print(f"❌ {model_name}: 转换失败 ({status})")
    
    print(f"\n📈 成功率: {success_count}/{len(all_results)} ({success_count/len(all_results)*100:.1f}%)")
    print(f"📋 详细报告: {report_path}")
    
    if success_count > 0:
        print(f"\n🎉 转换完成！可以在 {output_dir} 目录中找到转换后的模型。")

if __name__ == "__main__":
    main()
