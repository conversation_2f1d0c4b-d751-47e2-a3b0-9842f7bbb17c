#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
方案1改进计划 - 提升各类别识别准确率
================================================================================
目标：将方案1的总体准确率从74.6%提升到85%+，同时保持类别间均衡性
================================================================================
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from pathlib import Path
import json
from collections import Counter
import librosa
import tensorflow as tf
import tensorflow_hub as hub

class ImprovedFeatureExtractor:
    """改进的特征提取器 - 针对弱势类别优化"""
    
    def __init__(self):
        self.sr = 16000
        # 加载YAMNet模型
        self.yamnet = hub.load('https://tfhub.dev/google/yamnet/1')
        
    def extract_enhanced_features(self, audio_path, target_length=16000*3):
        """提取增强特征 - 针对不适和疲倦类别优化"""
        try:
            # 1. 基础YAMNet特征
            yamnet_features = self._extract_yamnet_features(audio_path, target_length)
            
            # 2. 针对弱势类别的专门特征
            specialized_features = self._extract_specialized_features(audio_path, target_length)
            
            # 3. 时频域特征增强
            temporal_features = self._extract_temporal_features(audio_path, target_length)
            
            # 4. 情感相关特征
            emotional_features = self._extract_emotional_features(audio_path, target_length)
            
            # 合并所有特征
            all_features = np.concatenate([
                yamnet_features,      # 5120维
                specialized_features, # 512维
                temporal_features,    # 256维
                emotional_features    # 128维
            ])
            
            return all_features.astype(np.float32)  # 总计6016维
            
        except Exception as e:
            print(f"特征提取失败: {e}")
            return self._fallback_features(audio_path, target_length)
    
    def _extract_yamnet_features(self, audio_path, target_length):
        """YAMNet特征提取"""
        audio, sr = librosa.load(audio_path, sr=self.sr)
        if len(audio) < target_length:
            audio = np.pad(audio, (0, target_length - len(audio)), mode='constant')
        else:
            audio = audio[:target_length]
        
        audio_tensor = tf.convert_to_tensor(audio, dtype=tf.float32)
        _, embeddings, _ = self.yamnet(audio_tensor)
        embeddings_np = embeddings.numpy()
        
        # 多种统计特征
        features = np.concatenate([
            np.mean(embeddings_np, axis=0),      # 1024维
            np.std(embeddings_np, axis=0),       # 1024维
            np.max(embeddings_np, axis=0),       # 1024维
            np.min(embeddings_np, axis=0),       # 1024维
            np.median(embeddings_np, axis=0),    # 1024维
        ])
        
        return features
    
    def _extract_specialized_features(self, audio_path, target_length):
        """针对不适和疲倦类别的专门特征"""
        audio, sr = librosa.load(audio_path, sr=self.sr)
        if len(audio) < target_length:
            audio = np.pad(audio, (0, target_length - len(audio)), mode='constant')
        else:
            audio = audio[:target_length]
        
        features = []
        
        # 1. 低频能量特征（疲倦哭声通常频率较低）
        stft = librosa.stft(audio, n_fft=2048, hop_length=512)
        magnitude = np.abs(stft)
        low_freq_energy = np.mean(magnitude[:50, :], axis=0)  # 低频段
        features.extend([
            np.mean(low_freq_energy),
            np.std(low_freq_energy),
            np.max(low_freq_energy),
            np.min(low_freq_energy)
        ])
        
        # 2. 音调变化特征（不适哭声音调变化较大）
        pitches, magnitudes = librosa.piptrack(y=audio, sr=sr)
        pitch_values = []
        for t in range(pitches.shape[1]):
            index = magnitudes[:, t].argmax()
            pitch = pitches[index, t]
            if pitch > 0:
                pitch_values.append(pitch)
        
        if len(pitch_values) > 0:
            pitch_values = np.array(pitch_values)
            features.extend([
                np.mean(pitch_values),
                np.std(pitch_values),
                np.max(pitch_values) - np.min(pitch_values),  # 音调范围
                len(pitch_values) / len(audio) * sr  # 音调密度
            ])
        else:
            features.extend([0, 0, 0, 0])
        
        # 3. 能量包络特征
        envelope = librosa.onset.onset_strength(y=audio, sr=sr)
        features.extend([
            np.mean(envelope),
            np.std(envelope),
            np.max(envelope),
            np.sum(envelope > np.mean(envelope))  # 高能量点数量
        ])
        
        # 填充到512维
        while len(features) < 512:
            features.extend([0] * min(12, 512 - len(features)))
        
        return np.array(features[:512])
    
    def _extract_temporal_features(self, audio_path, target_length):
        """时域特征提取"""
        audio, sr = librosa.load(audio_path, sr=self.sr)
        if len(audio) < target_length:
            audio = np.pad(audio, (0, target_length - len(audio)), mode='constant')
        else:
            audio = audio[:target_length]
        
        features = []
        
        # 1. 零交叉率
        zcr = librosa.feature.zero_crossing_rate(audio)[0]
        features.extend([np.mean(zcr), np.std(zcr)])
        
        # 2. 短时能量
        frame_length = 2048
        hop_length = 512
        frames = librosa.util.frame(audio, frame_length=frame_length, hop_length=hop_length)
        energy = np.sum(frames**2, axis=0)
        features.extend([np.mean(energy), np.std(energy), np.max(energy)])
        
        # 3. 频谱重心
        spectral_centroids = librosa.feature.spectral_centroid(y=audio, sr=sr)[0]
        features.extend([np.mean(spectral_centroids), np.std(spectral_centroids)])
        
        # 4. 频谱带宽
        spectral_bandwidth = librosa.feature.spectral_bandwidth(y=audio, sr=sr)[0]
        features.extend([np.mean(spectral_bandwidth), np.std(spectral_bandwidth)])
        
        # 填充到256维
        while len(features) < 256:
            features.extend([0] * min(8, 256 - len(features)))
        
        return np.array(features[:256])
    
    def _extract_emotional_features(self, audio_path, target_length):
        """情感相关特征"""
        audio, sr = librosa.load(audio_path, sr=self.sr)
        if len(audio) < target_length:
            audio = np.pad(audio, (0, target_length - len(audio)), mode='constant')
        else:
            audio = audio[:target_length]
        
        features = []
        
        # 1. MFCC特征（前13个系数）
        mfccs = librosa.feature.mfcc(y=audio, sr=sr, n_mfcc=13)
        for i in range(13):
            features.extend([
                np.mean(mfccs[i]),
                np.std(mfccs[i])
            ])
        
        # 2. 色度特征
        chroma = librosa.feature.chroma(y=audio, sr=sr)
        features.extend([
            np.mean(chroma),
            np.std(chroma),
            np.max(chroma)
        ])
        
        # 3. 频谱对比度
        contrast = librosa.feature.spectral_contrast(y=audio, sr=sr)
        features.extend([
            np.mean(contrast),
            np.std(contrast)
        ])
        
        # 填充到128维
        while len(features) < 128:
            features.extend([0] * min(5, 128 - len(features)))
        
        return np.array(features[:128])
    
    def _fallback_features(self, audio_path, target_length):
        """备用特征提取"""
        return np.zeros(6016, dtype=np.float32)


class ImprovedDeepInfantModel(nn.Module):
    """改进的深度学习模型 - 针对类别不平衡优化"""
    
    def __init__(self, input_dim=6016, num_classes=5, dropout=0.3):
        super(ImprovedDeepInfantModel, self).__init__()
        
        # 特征预处理层
        self.feature_norm = nn.BatchNorm1d(input_dim)
        
        # 多分支特征提取器
        self.yamnet_branch = self._create_branch(5120, [2048, 1024, 512])
        self.specialized_branch = self._create_branch(512, [256, 128, 64])
        self.temporal_branch = self._create_branch(256, [128, 64, 32])
        self.emotional_branch = self._create_branch(128, [64, 32, 16])
        
        # 特征融合层
        fusion_dim = 512 + 64 + 32 + 16
        self.fusion = nn.Sequential(
            nn.Linear(fusion_dim, 512),
            nn.BatchNorm1d(512),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout),
            
            nn.Linear(512, 256),
            nn.BatchNorm1d(256),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout)
        )
        
        # 类别特定的专家网络
        self.class_experts = nn.ModuleList([
            self._create_expert(256, 128) for _ in range(num_classes)
        ])
        
        # 最终分类器
        self.classifier = nn.Linear(128, num_classes)
        
        # 注意力机制
        self.attention = nn.Sequential(
            nn.Linear(256, 128),
            nn.Tanh(),
            nn.Linear(128, num_classes),
            nn.Softmax(dim=1)
        )
        
        self._initialize_weights()
    
    def _create_branch(self, input_dim, hidden_dims):
        """创建分支网络"""
        layers = []
        prev_dim = input_dim
        
        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.BatchNorm1d(hidden_dim),
                nn.ReLU(inplace=True),
                nn.Dropout(0.2)
            ])
            prev_dim = hidden_dim
        
        return nn.Sequential(*layers)
    
    def _create_expert(self, input_dim, output_dim):
        """创建专家网络"""
        return nn.Sequential(
            nn.Linear(input_dim, output_dim),
            nn.BatchNorm1d(output_dim),
            nn.ReLU(inplace=True),
            nn.Dropout(0.1)
        )
    
    def forward(self, x):
        # 特征归一化
        x = self.feature_norm(x)
        
        # 分离不同类型的特征
        yamnet_features = x[:, :5120]
        specialized_features = x[:, 5120:5632]
        temporal_features = x[:, 5632:5888]
        emotional_features = x[:, 5888:]
        
        # 多分支处理
        yamnet_out = self.yamnet_branch(yamnet_features)
        specialized_out = self.specialized_branch(specialized_features)
        temporal_out = self.temporal_branch(temporal_features)
        emotional_out = self.emotional_branch(emotional_features)
        
        # 特征融合
        fused_features = torch.cat([yamnet_out, specialized_out, temporal_out, emotional_out], dim=1)
        fused_features = self.fusion(fused_features)
        
        # 注意力权重
        attention_weights = self.attention(fused_features)
        
        # 专家网络处理
        expert_outputs = []
        for expert in self.class_experts:
            expert_outputs.append(expert(fused_features))
        
        expert_outputs = torch.stack(expert_outputs, dim=2)  # [batch, 128, num_classes]
        
        # 加权融合专家输出
        weighted_output = torch.sum(expert_outputs * attention_weights.unsqueeze(1), dim=2)
        
        # 最终分类
        output = self.classifier(weighted_output)
        
        return output
    
    def _initialize_weights(self):
        """权重初始化"""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.BatchNorm1d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)


# 改进策略总结
IMPROVEMENT_STRATEGIES = {
    "特征工程改进": [
        "增加针对不适和疲倦类别的专门特征",
        "提取低频能量特征（疲倦哭声特征）",
        "增强音调变化特征（不适哭声特征）",
        "添加情感相关的MFCC和色度特征"
    ],
    
    "模型架构改进": [
        "多分支特征处理网络",
        "类别特定的专家网络",
        "注意力机制加权融合",
        "更深层的特征提取"
    ],
    
    "训练策略改进": [
        "类别平衡的损失函数",
        "针对弱势类别的数据增强",
        "渐进式学习率调整",
        "早停和模型集成"
    ],
    
    "数据处理改进": [
        "智能数据增强（时间拉伸、音调变化）",
        "类别平衡采样",
        "噪声注入训练",
        "交叉验证优化"
    ]
}

if __name__ == "__main__":
    print("🚀 方案1改进计划")
    print("=" * 80)
    
    for strategy, items in IMPROVEMENT_STRATEGIES.items():
        print(f"\n📋 {strategy}:")
        for item in items:
            print(f"  • {item}")
    
    print(f"\n🎯 预期提升:")
    print(f"  • 总体准确率: 74.6% → 85%+")
    print(f"  • 不适类别: 33.3% → 60%+")
    print(f"  • 疲倦类别: 33.3% → 60%+")
    print(f"  • 保持其他类别高性能")
