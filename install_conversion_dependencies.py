#!/usr/bin/env python3
"""
Core ML 到 PyTorch 转换工具依赖安装脚本

这个脚本会自动安装最佳实践转换所需的所有依赖包。

作者: DeepInfant Team
"""

import subprocess
import sys
import importlib
from pathlib import Path

def check_package(package_name, import_name=None):
    """检查包是否已安装"""
    if import_name is None:
        import_name = package_name
    
    try:
        importlib.import_module(import_name)
        return True
    except ImportError:
        return False

def install_package(package_name):
    """安装包"""
    print(f"📦 安装 {package_name}...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
        print(f"✅ {package_name} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {package_name} 安装失败: {e}")
        return False

def main():
    print("🚀 Core ML 到 PyTorch 转换工具依赖安装")
    print("=" * 50)
    
    # 定义所需的包
    required_packages = [
        ("coremltools", "coremltools"),
        ("torch", "torch"),
        ("onnx", "onnx"),
        ("onnx-simplifier", "onnxsim"),
        ("onnx2pytorch", "onnx2pytorch"),
        ("librosa", "librosa"),  # 用于音频处理
        ("numpy", "numpy"),
        ("pathlib", "pathlib")  # Python 3.4+ 内置
    ]
    
    # 检查已安装的包
    print("🔍 检查已安装的包...")
    installed = []
    missing = []
    
    for package_name, import_name in required_packages:
        if import_name == "pathlib":  # 跳过内置包
            continue
            
        if check_package(package_name, import_name):
            print(f"✅ {package_name} 已安装")
            installed.append(package_name)
        else:
            print(f"❌ {package_name} 未安装")
            missing.append(package_name)
    
    if not missing:
        print("\n🎉 所有依赖都已安装！")
        return
    
    print(f"\n📋 需要安装 {len(missing)} 个包:")
    for package in missing:
        print(f"   - {package}")
    
    # 询问是否安装
    response = input("\n是否现在安装这些包？(y/n): ").lower().strip()
    if response not in ['y', 'yes', '是']:
        print("取消安装。")
        return
    
    # 安装缺失的包
    print("\n🔧 开始安装...")
    success_count = 0
    
    for package in missing:
        if install_package(package):
            success_count += 1
    
    # 安装结果
    print(f"\n📊 安装结果:")
    print(f"✅ 成功: {success_count}/{len(missing)}")
    
    if success_count == len(missing):
        print("🎉 所有依赖安装完成！")
        print("\n现在可以运行转换工具:")
        print("python coreml_to_pytorch_best_practice.py")
    else:
        print("⚠️ 部分依赖安装失败，请手动安装:")
        failed = [pkg for pkg in missing if not check_package(pkg)]
        for package in failed:
            print(f"pip install {package}")

if __name__ == "__main__":
    main()
