#!/usr/bin/env python3
"""
综合性能对比报告

这个脚本生成所有方案的综合性能对比报告。

作者: DeepInfant Team
"""

import json
import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from pathlib import Path

def load_results():
    """加载所有方案的结果"""
    results = {}
    
    # 方案1结果
    v1_path = Path("stable_v1_performance_report.json")
    if v1_path.exists():
        with open(v1_path, 'r', encoding='utf-8') as f:
            results['方案1 (GPU稳定训练)'] = json.load(f)
    
    # 方案2结果
    v2_path = Path("ensemble_v2_performance_report.json")
    if v2_path.exists():
        with open(v2_path, 'r', encoding='utf-8') as f:
            results['方案2 (集成学习)'] = json.load(f)
    
    # 方案3结果
    v3_path = Path("advanced_v3_performance_report.json")
    if v3_path.exists():
        with open(v3_path, 'r', encoding='utf-8') as f:
            results['方案3 (高级特征)'] = json.load(f)
    
    return results

def generate_comprehensive_report():
    """生成综合性能报告"""
    print("📊 生成综合性能对比报告")
    print("=" * 80)
    
    # 加载结果
    results = load_results()
    
    if not results:
        print("❌ 没有找到任何测试结果文件")
        return
    
    # 基准数据
    baseline_data = {
        '原始模型': 13.1,
        '简化训练': 38.9
    }
    
    print("🎯 总体性能对比")
    print("-" * 80)
    
    # 总体准确率对比
    print(f"{'方案':<20} {'总体准确率':<15} {'相比原始提升':<15} {'相比简化提升':<15}")
    print("-" * 80)
    
    # 基准数据
    for name, acc in baseline_data.items():
        improvement_original = acc - 13.1
        improvement_simple = acc - 38.9
        print(f"{name:<20} {acc:<14.1f}% {improvement_original:<14.1f}% {improvement_simple:<14.1f}%")
    
    # 新方案数据
    for method_name, data in results.items():
        acc = data['overall_accuracy'] * 100
        improvement_original = data['improvement_over_original']
        improvement_simple = data['improvement_over_simple']
        print(f"{method_name:<20} {acc:<14.1f}% {improvement_original:<14.1f}% {improvement_simple:<14.1f}%")
    
    print("\n🏆 最佳性能方案")
    print("-" * 80)
    
    # 找到最佳方案
    best_method = None
    best_accuracy = 0
    
    for method_name, data in results.items():
        acc = data['overall_accuracy'] * 100
        if acc > best_accuracy:
            best_accuracy = acc
            best_method = method_name
    
    if best_method:
        print(f"🥇 最佳方案: {best_method}")
        print(f"   总体准确率: {best_accuracy:.1f}%")
        print(f"   相比目标(89%)差距: {best_accuracy - 89:.1f} 个百分点")
        
        if best_accuracy >= 89:
            print("   ✅ 已达到89%目标准确率！")
        else:
            print(f"   ⚠️ 距离目标还差 {89 - best_accuracy:.1f} 个百分点")
    
    print("\n📈 各类别性能对比")
    print("-" * 80)
    
    # 类别映射
    category_names = {
        'belly_pain': '腹痛',
        'burping': '打嗝', 
        'discomfort': '不适',
        'hungry': '饥饿',
        'tired': '疲倦'
    }
    
    # 创建类别性能表
    category_performance = {}
    
    for method_name, data in results.items():
        category_performance[method_name] = {}
        for dir_name, dir_data in data['dir_accuracies'].items():
            category_performance[method_name][category_names.get(dir_name, dir_name)] = dir_data['accuracy'] * 100
    
    # 显示类别性能表
    categories = list(category_names.values())
    print(f"{'类别':<10}", end="")
    for method_name in results.keys():
        print(f"{method_name:<20}", end="")
    print()
    print("-" * (10 + 20 * len(results)))
    
    for category in categories:
        print(f"{category:<10}", end="")
        for method_name in results.keys():
            acc = category_performance[method_name].get(category, 0)
            print(f"{acc:<19.1f}%", end="")
        print()
    
    print("\n🔍 详细分析")
    print("-" * 80)
    
    # 分析每个方案的优缺点
    for method_name, data in results.items():
        print(f"\n📋 {method_name}:")
        print(f"   总体准确率: {data['overall_accuracy']*100:.1f}%")
        print(f"   测试样本数: {data['total_samples']}")
        print(f"   平均推理时间: {data['avg_inference_time']:.3f}s")
        
        # 找出表现最好和最差的类别
        category_accs = []
        for dir_name, dir_data in data['dir_accuracies'].items():
            category_accs.append((category_names.get(dir_name, dir_name), dir_data['accuracy'] * 100))
        
        category_accs.sort(key=lambda x: x[1], reverse=True)
        
        print(f"   最佳类别: {category_accs[0][0]} ({category_accs[0][1]:.1f}%)")
        print(f"   最差类别: {category_accs[-1][0]} ({category_accs[-1][1]:.1f}%)")
        
        # 计算类别间的标准差
        accs = [acc for _, acc in category_accs]
        std_dev = np.std(accs)
        print(f"   类别间标准差: {std_dev:.1f}% (越小越均衡)")
    
    print("\n💡 结论与建议")
    print("-" * 80)
    
    # 生成结论
    if best_method and best_accuracy >= 89:
        print("🎉 恭喜！已成功达到89%的目标准确率！")
        print(f"   推荐使用: {best_method}")
        print("   建议:")
        print("   - 可以将此模型部署到生产环境")
        print("   - 继续收集更多数据以进一步提升性能")
        print("   - 考虑模型压缩和优化以提升推理速度")
    else:
        print("📈 虽然未完全达到89%目标，但已取得显著进步！")
        if best_method:
            print(f"   当前最佳方案: {best_method} ({best_accuracy:.1f}%)")
        
        print("   进一步提升建议:")
        print("   - 收集更多训练数据，特别是表现较差的类别")
        print("   - 尝试模型融合，结合多个方案的优势")
        print("   - 进行超参数优化")
        print("   - 考虑使用更大的预训练模型")
    
    # 保存综合报告
    comprehensive_data = {
        'baseline_data': baseline_data,
        'method_results': results,
        'best_method': best_method,
        'best_accuracy': best_accuracy,
        'category_performance': category_performance,
        'target_achieved': best_accuracy >= 89 if best_method else False
    }
    
    report_path = Path("comprehensive_performance_report.json")
    with open(report_path, 'w', encoding='utf-8') as f:
        json.dump(comprehensive_data, f, indent=2, ensure_ascii=False, default=str)
    
    print(f"\n📋 综合报告已保存: {report_path}")
    
    return comprehensive_data

def create_performance_visualization():
    """创建性能可视化图表"""
    try:
        import matplotlib.pyplot as plt
        import matplotlib
        matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
        matplotlib.rcParams['axes.unicode_minus'] = False
        
        results = load_results()
        if not results:
            return
        
        # 创建总体性能对比图
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # 总体准确率对比
        methods = ['原始模型', '简化训练'] + list(results.keys())
        accuracies = [13.1, 38.9] + [data['overall_accuracy'] * 100 for data in results.values()]
        colors = ['red', 'orange'] + ['green', 'blue', 'purple'][:len(results)]
        
        bars1 = ax1.bar(methods, accuracies, color=colors)
        ax1.set_title('总体准确率对比', fontsize=14, fontweight='bold')
        ax1.set_ylabel('准确率 (%)', fontsize=12)
        ax1.set_ylim(0, 100)
        
        # 添加目标线
        ax1.axhline(y=89, color='red', linestyle='--', alpha=0.7, label='目标准确率 (89%)')
        ax1.legend()
        
        # 在柱子上添加数值
        for bar, acc in zip(bars1, accuracies):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 1,
                    f'{acc:.1f}%', ha='center', va='bottom', fontweight='bold')
        
        # 旋转x轴标签
        plt.setp(ax1.get_xticklabels(), rotation=45, ha='right')
        
        # 类别性能热力图
        if results:
            category_names = ['腹痛', '打嗝', '不适', '饥饿', '疲倦']
            method_names = list(results.keys())
            
            # 创建性能矩阵
            performance_matrix = []
            dir_mapping = {'belly_pain': '腹痛', 'burping': '打嗝', 'discomfort': '不适', 
                          'hungry': '饥饿', 'tired': '疲倦'}
            
            for method_name in method_names:
                method_performance = []
                for category in category_names:
                    # 找到对应的目录名
                    dir_name = None
                    for k, v in dir_mapping.items():
                        if v == category:
                            dir_name = k
                            break
                    
                    if dir_name and dir_name in results[method_name]['dir_accuracies']:
                        acc = results[method_name]['dir_accuracies'][dir_name]['accuracy'] * 100
                    else:
                        acc = 0
                    method_performance.append(acc)
                performance_matrix.append(method_performance)
            
            im = ax2.imshow(performance_matrix, cmap='RdYlGn', aspect='auto', vmin=0, vmax=100)
            
            # 设置标签
            ax2.set_xticks(range(len(category_names)))
            ax2.set_xticklabels(category_names)
            ax2.set_yticks(range(len(method_names)))
            ax2.set_yticklabels([name.replace('方案', '方案\n') for name in method_names])
            
            # 添加数值
            for i in range(len(method_names)):
                for j in range(len(category_names)):
                    text = ax2.text(j, i, f'{performance_matrix[i][j]:.1f}%',
                                   ha="center", va="center", color="black", fontweight='bold')
            
            ax2.set_title('各类别准确率热力图', fontsize=14, fontweight='bold')
            
            # 添加颜色条
            cbar = plt.colorbar(im, ax=ax2)
            cbar.set_label('准确率 (%)', rotation=270, labelpad=15)
        
        plt.tight_layout()
        plt.savefig('performance_comparison.png', dpi=300, bbox_inches='tight')
        print("📊 性能对比图表已保存: performance_comparison.png")
        
    except ImportError:
        print("⚠️ matplotlib未安装，跳过图表生成")
    except Exception as e:
        print(f"⚠️ 图表生成失败: {e}")

def main():
    print("🚀 综合性能分析报告")
    print("=" * 80)
    print("分析所有方案的性能表现")
    print("=" * 80)
    
    # 生成综合报告
    comprehensive_data = generate_comprehensive_report()
    
    # 创建可视化图表
    create_performance_visualization()
    
    print("\n" + "=" * 80)
    print("📋 报告生成完成！")
    print("=" * 80)
    
    if comprehensive_data and comprehensive_data.get('target_achieved'):
        print("🎉 恭喜！已成功达到89%的目标准确率！")
    else:
        print("📈 继续努力，向89%目标迈进！")

if __name__ == "__main__":
    main()
