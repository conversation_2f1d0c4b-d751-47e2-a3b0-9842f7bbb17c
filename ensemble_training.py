#!/usr/bin/env python3
"""
集成学习 + 数据增强方案

通过训练多个不同的模型并集成它们的预测来提升性能。

作者: DeepInfant Team
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import librosa
import numpy as np
from pathlib import Path
import json
from collections import Counter
from sklearn.model_selection import train_test_split
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

class AudioAugmentation:
    """音频数据增强"""
    
    def __init__(self):
        pass
    
    def time_stretch(self, audio, rate=1.0):
        """时间拉伸"""
        return librosa.effects.time_stretch(audio, rate=rate)
    
    def pitch_shift(self, audio, sr, n_steps=0):
        """音调变化"""
        return librosa.effects.pitch_shift(audio, sr=sr, n_steps=n_steps)
    
    def add_noise(self, audio, noise_factor=0.005):
        """添加噪声"""
        noise = np.random.randn(len(audio))
        return audio + noise_factor * noise
    
    def augment_audio(self, audio, sr):
        """随机应用增强"""
        augmented_audios = [audio]  # 原始音频
        
        # 时间拉伸
        for rate in [0.9, 1.1]:
            try:
                stretched = self.time_stretch(audio, rate)
                augmented_audios.append(stretched)
            except:
                pass
        
        # 音调变化
        for n_steps in [-2, 2]:
            try:
                shifted = self.pitch_shift(audio, sr, n_steps)
                augmented_audios.append(shifted)
            except:
                pass
        
        # 添加噪声
        for noise_factor in [0.003, 0.007]:
            noisy = self.add_noise(audio, noise_factor)
            augmented_audios.append(noisy)
        
        return augmented_audios

class MultiFeatureExtractor:
    """多种特征提取器"""
    
    def __init__(self):
        self.augmentation = AudioAugmentation()
    
    def extract_spectral_features(self, audio, sr):
        """频谱特征"""
        features = []
        
        # Mel频谱
        mel_spec = librosa.feature.melspectrogram(y=audio, sr=sr, n_mels=128)
        mel_spec_db = librosa.power_to_db(mel_spec)
        features.extend([
            np.mean(mel_spec_db, axis=1),
            np.std(mel_spec_db, axis=1),
            np.max(mel_spec_db, axis=1),
            np.min(mel_spec_db, axis=1)
        ])
        
        # MFCC
        mfcc = librosa.feature.mfcc(y=audio, sr=sr, n_mfcc=20)
        features.extend([
            np.mean(mfcc, axis=1),
            np.std(mfcc, axis=1),
            np.max(mfcc, axis=1),
            np.min(mfcc, axis=1)
        ])
        
        return np.concatenate([f.flatten() for f in features])
    
    def extract_temporal_features(self, audio, sr):
        """时域特征"""
        features = []
        
        # 零交叉率
        zcr = librosa.feature.zero_crossing_rate(audio)
        features.extend([np.mean(zcr), np.std(zcr)])
        
        # RMS能量
        rms = librosa.feature.rms(y=audio)
        features.extend([np.mean(rms), np.std(rms)])
        
        # 谱质心
        spectral_centroids = librosa.feature.spectral_centroid(y=audio, sr=sr)
        features.extend([np.mean(spectral_centroids), np.std(spectral_centroids)])
        
        # 谱带宽
        spectral_bandwidth = librosa.feature.spectral_bandwidth(y=audio, sr=sr)
        features.extend([np.mean(spectral_bandwidth), np.std(spectral_bandwidth)])
        
        # 谱滚降
        spectral_rolloff = librosa.feature.spectral_rolloff(y=audio, sr=sr)
        features.extend([np.mean(spectral_rolloff), np.std(spectral_rolloff)])
        
        return np.array(features)
    
    def extract_features(self, audio_path, augment=False):
        """提取特征"""
        try:
            audio, sr = librosa.load(audio_path, sr=16000)
            
            # 调整长度
            target_length = 16000 * 3
            if len(audio) < target_length:
                audio = np.pad(audio, (0, target_length - len(audio)), mode='constant')
            else:
                audio = audio[:target_length]
            
            all_features = []
            
            # 原始音频特征
            spectral_features = self.extract_spectral_features(audio, sr)
            temporal_features = self.extract_temporal_features(audio, sr)
            features = np.concatenate([spectral_features, temporal_features])
            all_features.append(features)
            
            # 数据增强
            if augment:
                augmented_audios = self.augmentation.augment_audio(audio, sr)
                for aug_audio in augmented_audios[1:]:  # 跳过原始音频
                    try:
                        # 调整长度
                        if len(aug_audio) < target_length:
                            aug_audio = np.pad(aug_audio, (0, target_length - len(aug_audio)), mode='constant')
                        else:
                            aug_audio = aug_audio[:target_length]
                        
                        spectral_features = self.extract_spectral_features(aug_audio, sr)
                        temporal_features = self.extract_temporal_features(aug_audio, sr)
                        features = np.concatenate([spectral_features, temporal_features])
                        all_features.append(features)
                    except:
                        pass
            
            return all_features
            
        except Exception as e:
            print(f"特征提取失败: {e}")
            return [np.zeros(600, dtype=np.float32)]

class EnsembleModel(nn.Module):
    """集成模型"""
    
    def __init__(self, input_dim, num_classes=5):
        super(EnsembleModel, self).__init__()
        
        # 三个不同的子网络
        self.network1 = self._create_network(input_dim, num_classes, [512, 256, 128])
        self.network2 = self._create_network(input_dim, num_classes, [256, 128, 64])
        self.network3 = self._create_network(input_dim, num_classes, [1024, 512, 256])
        
        # 集成层
        self.ensemble_layer = nn.Linear(num_classes * 3, num_classes)
        
    def _create_network(self, input_dim, num_classes, hidden_dims):
        """创建子网络"""
        layers = []
        prev_dim = input_dim
        
        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.BatchNorm1d(hidden_dim),
                nn.ReLU(inplace=True),
                nn.Dropout(0.3)
            ])
            prev_dim = hidden_dim
        
        layers.append(nn.Linear(prev_dim, num_classes))
        return nn.Sequential(*layers)
    
    def forward(self, x):
        # 三个子网络的输出
        out1 = self.network1(x)
        out2 = self.network2(x)
        out3 = self.network3(x)
        
        # 拼接输出
        combined = torch.cat([out1, out2, out3], dim=1)
        
        # 集成预测
        final_output = self.ensemble_layer(combined)
        
        return final_output

class AugmentedDataset(Dataset):
    """增强数据集"""
    
    def __init__(self, data_dir, feature_extractor, split='train', augment=True):
        self.feature_extractor = feature_extractor
        self.augment = augment and (split == 'train')
        self.label_map = {'bp': 0, 'bu': 1, 'dc': 2, 'hu': 3, 'ti': 4}
        
        # 收集数据
        self.data = self._collect_data(data_dir)
        
        # 划分数据集
        if len(self.data) > 0:
            train_data, test_data = train_test_split(
                self.data, test_size=0.2, random_state=42, 
                stratify=[item[1] for item in self.data]
            )
            
            if split == 'train':
                self.data = train_data
            else:
                self.data = test_data
        
        print(f"{split} 集大小: {len(self.data)}")
        if len(self.data) > 0:
            label_counts = Counter([item[1] for item in self.data])
            print(f"标签分布: {dict(label_counts)}")
    
    def _collect_data(self, data_dir):
        """收集数据"""
        data = []
        data_dir = Path(data_dir)
        
        dir_to_label = {
            'belly_pain': 'bp', 'burping': 'bu', 'discomfort': 'dc',
            'hungry': 'hu', 'tired': 'ti'
        }
        
        for dir_name, label in dir_to_label.items():
            dir_path = data_dir / dir_name
            if dir_path.exists():
                audio_files = list(dir_path.glob('*.wav'))
                for audio_file in audio_files:
                    data.append((str(audio_file), self.label_map[label]))
                print(f"从 {dir_name} 收集了 {len(audio_files)} 个文件")
        
        return data
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        audio_path, label = self.data[idx]
        
        # 提取特征（可能包含增强）
        features_list = self.feature_extractor.extract_features(audio_path, augment=self.augment)
        
        # 随机选择一个特征（如果有增强）
        features = features_list[np.random.randint(len(features_list))]
        
        return torch.FloatTensor(features), torch.LongTensor([label])[0]

def train_ensemble_model():
    """训练集成模型"""
    print("🚀 集成学习 + 数据增强训练")
    print("=" * 60)
    
    # 设备选择
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 创建输出目录
    output_dir = Path("ensemble_training_results")
    output_dir.mkdir(exist_ok=True)
    
    # 初始化特征提取器
    feature_extractor = MultiFeatureExtractor()
    
    # 创建数据集
    train_dataset = AugmentedDataset('Data', feature_extractor, split='train', augment=True)
    val_dataset = AugmentedDataset('Data', feature_extractor, split='test', augment=False)
    
    if len(train_dataset) == 0:
        print("❌ 数据集为空")
        return
    
    # 获取特征维度
    sample_features, _ = train_dataset[0]
    input_dim = sample_features.shape[0]
    print(f"特征维度: {input_dim}")
    
    # 创建数据加载器
    train_loader = DataLoader(train_dataset, batch_size=16, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=16, shuffle=False)
    
    # 创建集成模型
    model = EnsembleModel(input_dim=input_dim, num_classes=5)
    model = model.to(device)
    
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 训练配置
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.AdamW(model.parameters(), lr=0.001, weight_decay=0.01)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=10, factor=0.5)
    
    # 训练循环
    best_val_acc = 0.0
    history = {'train_loss': [], 'train_acc': [], 'val_loss': [], 'val_acc': []}
    
    for epoch in range(100):
        # 训练
        model.train()
        train_loss = 0.0
        train_correct = 0
        train_total = 0
        
        for features, labels in tqdm(train_loader, desc=f'Epoch {epoch+1} [Train]'):
            features, labels = features.to(device), labels.to(device)
            
            optimizer.zero_grad()
            outputs = model(features)
            loss = criterion(outputs, labels)
            loss.backward()
            
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()
            
            train_loss += loss.item()
            _, predicted = torch.max(outputs.data, 1)
            train_total += labels.size(0)
            train_correct += (predicted == labels).sum().item()
        
        # 验证
        model.eval()
        val_loss = 0.0
        val_correct = 0
        val_total = 0
        
        with torch.no_grad():
            for features, labels in tqdm(val_loader, desc=f'Epoch {epoch+1} [Val]'):
                features, labels = features.to(device), labels.to(device)
                outputs = model(features)
                loss = criterion(outputs, labels)
                
                val_loss += loss.item()
                _, predicted = torch.max(outputs.data, 1)
                val_total += labels.size(0)
                val_correct += (predicted == labels).sum().item()
        
        # 计算指标
        avg_train_loss = train_loss / len(train_loader)
        avg_train_acc = 100. * train_correct / train_total
        avg_val_loss = val_loss / len(val_loader)
        avg_val_acc = 100. * val_correct / val_total
        
        # 记录历史
        history['train_loss'].append(avg_train_loss)
        history['train_acc'].append(avg_train_acc)
        history['val_loss'].append(avg_val_loss)
        history['val_acc'].append(avg_val_acc)
        
        # 学习率调度
        scheduler.step(avg_val_loss)
        
        # 保存最佳模型
        if avg_val_acc > best_val_acc:
            best_val_acc = avg_val_acc
            torch.save(model.state_dict(), output_dir / "best_ensemble_model.pth")
        
        print(f'Epoch {epoch+1}: Train Acc: {avg_train_acc:.2f}%, Val Acc: {avg_val_acc:.2f}%')
        
        if epoch > 20 and avg_val_acc < best_val_acc - 10:  # 早停
            break
    
    # 保存最终模型
    torch.save(model, output_dir / "ensemble_full_model.pth")
    
    # 保存结果
    with open(output_dir / "ensemble_results.json", 'w') as f:
        json.dump(history, f, indent=2)
    
    print(f"✅ 集成模型训练完成！最佳验证准确率: {best_val_acc:.2f}%")
    return best_val_acc

if __name__ == "__main__":
    train_ensemble_model()
