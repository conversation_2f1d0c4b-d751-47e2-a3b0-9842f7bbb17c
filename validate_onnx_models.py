import onnxruntime as ort
import librosa
import numpy as np
from pathlib import Path
import json
from collections import defaultdict
import time

class ONNXInfantCryPredictor:
    def __init__(self, model_path):
        self.session = ort.InferenceSession(model_path)
        self.model_name = Path(model_path).stem
        
        # 更新的标签映射，与训练脚本一致
        self.label_map = {
            0: 'bp',  # belly pain
            1: 'bu',  # burping
            2: 'ch',  # cold/hot
            3: 'dc',  # discomfort
            4: 'hu',  # hungry
            5: 'lo',  # lonely
            6: 'sc',  # scared
            7: 'ti',  # tired
            8: 'un',  # unknown
        }
        
        self.label_names = {
            'bp': 'belly pain',
            'bu': 'burping',
            'ch': 'cold/hot',
            'dc': 'discomfort',
            'hu': 'hungry',
            'lo': 'lonely',
            'sc': 'scared',
            'ti': 'tired',
            'un': 'unknown'
        }
    
    def _process_audio(self, audio_path):
        """处理音频文件，与训练时保持一致"""
        # 加载音频，16kHz采样率
        waveform, sample_rate = librosa.load(audio_path, sr=16000)

        # 确保7秒长度
        target_length = 7 * 16000
        if len(waveform) > target_length:
            waveform = waveform[:target_length]
        else:
            waveform = np.pad(waveform, (0, target_length - len(waveform)))

        # 生成mel频谱图，与训练参数一致
        mel_spec = librosa.feature.melspectrogram(
            y=waveform,
            sr=sample_rate,
            n_fft=1024,
            hop_length=256,
            n_mels=80,
            fmin=20,
            fmax=8000
        )

        # 转换为对数刻度
        mel_spec = librosa.power_to_db(mel_spec, ref=np.max)

        # 确保时间维度为432（与模型期望一致）
        if mel_spec.shape[1] > 432:
            mel_spec = mel_spec[:, :432]
        elif mel_spec.shape[1] < 432:
            pad_width = 432 - mel_spec.shape[1]
            mel_spec = np.pad(mel_spec, ((0, 0), (0, pad_width)), mode='constant')

        # 添加batch和channel维度
        mel_spec = np.expand_dims(mel_spec, axis=0)  # channel维度
        mel_spec = np.expand_dims(mel_spec, axis=0)  # batch维度

        return mel_spec.astype(np.float32)
    
    def predict(self, audio_path):
        """预测单个音频文件"""
        # 处理音频
        input_data = self._process_audio(audio_path)

        # 获取模型输入名称
        input_name = self.session.get_inputs()[0].name

        # 运行推理
        start_time = time.time()
        outputs = self.session.run(None, {input_name: input_data})
        inference_time = time.time() - start_time

        probabilities = outputs[0][0]  # 移除batch维度

        # 获取预测结果
        predicted_class_idx = np.argmax(probabilities)
        confidence = probabilities[predicted_class_idx]
        predicted_label = self.label_map[predicted_class_idx]

        return predicted_label, confidence, inference_time
    
    def predict_batch(self, audio_dir, file_extensions=('.wav', '.caf', '.3gp')):
        """批量预测目录中的音频文件"""
        results = []
        audio_dir = Path(audio_dir)
        
        if not audio_dir.exists():
            print(f"目录不存在: {audio_dir}")
            return results
        
        audio_files = []
        for ext in file_extensions:
            audio_files.extend(audio_dir.glob(f'*{ext}'))
        
        print(f"在 {audio_dir} 中找到 {len(audio_files)} 个音频文件")
        
        for audio_file in audio_files:
            try:
                label, confidence, inference_time = self.predict(str(audio_file))
                results.append({
                    'filename': audio_file.name,
                    'predicted_label': label,
                    'predicted_name': self.label_names[label],
                    'confidence': confidence,
                    'inference_time': inference_time
                })
            except Exception as e:
                print(f"处理文件 {audio_file.name} 时出错: {e}")
                results.append({
                    'filename': audio_file.name,
                    'error': str(e)
                })
        
        return results

def extract_true_label_from_filename(filename, directory_name=None):
    """从文件名或目录名中提取真实标签"""
    # 首先尝试从文件名中提取标签，格式如: "sample-bp-001.wav"
    parts = filename.split('-')
    for part in parts:
        if len(part) == 2 and part.lower() in ['bp', 'bu', 'ch', 'dc', 'hu', 'lo', 'sc', 'ti', 'un']:
            return part.lower()

    # 如果从文件名无法提取，尝试从目录名提取
    if directory_name:
        dir_to_label = {
            'belly_pain': 'bp',
            'burping': 'bu',
            'discomfort': 'dc',
            'hungry': 'hu',
            'tired': 'ti'
        }
        return dir_to_label.get(directory_name.lower())

    return None

def calculate_accuracy(results, directory_name=None):
    """计算准确率"""
    correct = 0
    total = 0
    label_stats = defaultdict(lambda: {'correct': 0, 'total': 0})

    for result in results:
        if 'error' in result:
            continue

        filename = result['filename']
        predicted = result['predicted_label']

        # 尝试从文件名或目录名提取真实标签
        true_label = extract_true_label_from_filename(filename, directory_name)

        if true_label:
            total += 1
            label_stats[true_label]['total'] += 1

            if predicted == true_label:
                correct += 1
                label_stats[true_label]['correct'] += 1

    overall_accuracy = correct / total if total > 0 else 0

    return overall_accuracy, label_stats, total

def validate_models():
    """验证所有ONNX模型"""
    # 可用的ONNX模型
    onnx_models = [
        'DeepInfant_VGGish_Simple.onnx',
        'DeepInfant_Compact.onnx',
        'DeepInfant_VGGish_rebuilt.onnx',
        'DeepInfant_AFP_rebuilt.onnx'
    ]
    
    # 测试数据目录
    test_dirs = [
        'processed_dataset/test',
        'Data/belly_pain',
        'Data/burping',
        'Data/discomfort',
        'Data/hungry',
        'Data/tired'
    ]
    
    validation_results = {}
    
    for model_path in onnx_models:
        if not Path(model_path).exists():
            print(f"模型文件不存在: {model_path}")
            continue
            
        print(f"\n{'='*60}")
        print(f"验证模型: {model_path}")
        print('='*60)
        
        try:
            predictor = ONNXInfantCryPredictor(model_path)
            model_results = []
            
            for test_dir in test_dirs:
                if Path(test_dir).exists():
                    print(f"\n测试目录: {test_dir}")
                    results = predictor.predict_batch(test_dir)

                    # 为每个结果添加目录信息
                    dir_name = Path(test_dir).name
                    for result in results:
                        result['directory'] = dir_name

                    model_results.extend(results)

                    # 显示一些示例结果
                    for i, result in enumerate(results[:3]):
                        if 'error' not in result:
                            print(f"  {result['filename']}: {result['predicted_name']} "
                                  f"({result['confidence']:.3f}, {result['inference_time']:.3f}s)")

            # 计算总体准确率
            if model_results:
                # 按目录分别计算准确率
                dir_accuracies = {}
                total_correct = 0
                total_samples = 0

                for test_dir in test_dirs:
                    dir_name = Path(test_dir).name
                    dir_results = [r for r in model_results if r.get('directory') == dir_name]
                    if dir_results:
                        accuracy, label_stats, samples = calculate_accuracy(dir_results, dir_name)
                        dir_accuracies[dir_name] = {
                            'accuracy': accuracy,
                            'samples': samples,
                            'label_stats': label_stats
                        }
                        total_correct += sum(stats['correct'] for stats in label_stats.values())
                        total_samples += samples

                overall_accuracy = total_correct / total_samples if total_samples > 0 else 0
                
                print(f"\n📊 模型性能统计:")
                print(f"总样本数: {total_samples}")
                print(f"整体准确率: {overall_accuracy:.3f} ({overall_accuracy*100:.1f}%)")

                # 显示各目录的准确率
                if dir_accuracies:
                    print(f"\n各目录准确率:")
                    for dir_name, dir_stats in dir_accuracies.items():
                        print(f"  {dir_name}: {dir_stats['accuracy']:.3f} ({dir_stats['samples']} 样本)")
                        for label, stats in dir_stats['label_stats'].items():
                            label_acc = stats['correct'] / stats['total'] if stats['total'] > 0 else 0
                            print(f"    {label}: {label_acc:.3f} ({stats['correct']}/{stats['total']})")
                
                # 计算平均推理时间
                inference_times = [r['inference_time'] for r in model_results if 'inference_time' in r]
                avg_inference_time = np.mean(inference_times) if inference_times else 0
                print(f"\n平均推理时间: {avg_inference_time:.3f}s")
                
                validation_results[model_path] = {
                    'accuracy': overall_accuracy,
                    'total_samples': total_samples,
                    'dir_accuracies': dir_accuracies,
                    'avg_inference_time': avg_inference_time,
                    'results': model_results
                }
            else:
                print("未找到有效的测试样本")
                
        except Exception as e:
            print(f"验证模型 {model_path} 时出错: {e}")
    
    return validation_results

def save_validation_report(validation_results):
    """保存验证报告"""
    report = {
        'validation_timestamp': str(time.time()),
        'models': {}
    }
    
    for model_path, results in validation_results.items():
        # 移除详细结果以减少文件大小
        model_summary = {k: v for k, v in results.items() if k != 'results'}
        report['models'][model_path] = model_summary
    
    with open('onnx_validation_report.json', 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print(f"\n📋 验证报告已保存到: onnx_validation_report.json")

def main():
    print("🚀 开始验证ONNX模型...")
    
    # 检查ONNX Runtime
    try:
        import onnxruntime as ort
        print(f"ONNX Runtime版本: {ort.__version__}")
    except ImportError:
        print("❌ 请安装ONNX Runtime: pip install onnxruntime")
        return
    
    # 验证模型
    validation_results = validate_models()
    
    if validation_results:
        # 保存报告
        save_validation_report(validation_results)
        
        # 显示总结
        print(f"\n{'='*60}")
        print("🎯 验证总结")
        print('='*60)
        
        for model_path, results in validation_results.items():
            print(f"\n{model_path}:")
            print(f"  准确率: {results['accuracy']:.3f} ({results['accuracy']*100:.1f}%)")
            print(f"  样本数: {results['total_samples']}")
            print(f"  推理时间: {results['avg_inference_time']:.3f}s")
    else:
        print("❌ 没有成功验证任何模型")

if __name__ == "__main__":
    main()
