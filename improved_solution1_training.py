#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的方案1训练 - 针对弱势类别优化
================================================================================
目标：提升不适和疲倦类别的识别准确率，保持整体均衡性
================================================================================
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader, WeightedRandomSampler
import numpy as np
import librosa
import tensorflow as tf
import tensorflow_hub as hub
from pathlib import Path
import json
from collections import Counter
from sklearn.model_selection import train_test_split
from tqdm import tqdm
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

# 导入改进的模型
from improve_solution1_plan import ImprovedFeatureExtractor, ImprovedDeepInfantModel

class FocalLoss(nn.Module):
    """Focal Loss - 解决类别不平衡问题"""
    
    def __init__(self, alpha=None, gamma=2.0, reduction='mean'):
        super(FocalLoss, self).__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.reduction = reduction
        
    def forward(self, inputs, targets):
        ce_loss = nn.functional.cross_entropy(inputs, targets, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = (1 - pt) ** self.gamma * ce_loss
        
        if self.alpha is not None:
            if isinstance(self.alpha, (float, int)):
                alpha_t = self.alpha
            else:
                alpha_t = self.alpha[targets]
            focal_loss = alpha_t * focal_loss
            
        if self.reduction == 'mean':
            return focal_loss.mean()
        elif self.reduction == 'sum':
            return focal_loss.sum()
        else:
            return focal_loss

class ImprovedInfantCryDataset(Dataset):
    """改进的婴儿哭声数据集 - 增强数据处理"""
    
    def __init__(self, data_dir, feature_extractor, split='train', test_size=0.2, 
                 augment_weak_classes=True, random_state=42):
        self.feature_extractor = feature_extractor
        self.label_map = {'bp': 0, 'bu': 1, 'dc': 2, 'hu': 3, 'ti': 4}
        self.label_names = {0: 'belly_pain', 1: 'burping', 2: 'discomfort', 3: 'hungry', 4: 'tired'}
        self.augment_weak_classes = augment_weak_classes
        
        # 弱势类别（需要特别关注）
        self.weak_classes = [2, 4]  # discomfort, tired
        
        # 收集数据
        self.data = self._collect_data(data_dir)
        
        # 数据增强 - 针对弱势类别
        if augment_weak_classes and split == 'train':
            self.data = self._augment_weak_classes(self.data)
        
        # 划分数据集
        if len(self.data) > 0:
            train_data, test_data = train_test_split(
                self.data, test_size=test_size, random_state=random_state, 
                stratify=[item[1] for item in self.data]
            )
            
            if split == 'train':
                self.data = train_data
            else:
                self.data = test_data
        
        print(f"{split} 集大小: {len(self.data)}")
        if len(self.data) > 0:
            label_counts = Counter([item[1] for item in self.data])
            print(f"标签分布: {dict(label_counts)}")
    
    def _collect_data(self, data_dir):
        """收集数据"""
        data = []
        data_dir = Path(data_dir)
        
        dir_to_label = {
            'belly_pain': 'bp', 'burping': 'bu', 'discomfort': 'dc',
            'hungry': 'hu', 'tired': 'ti'
        }
        
        for dir_name, label in dir_to_label.items():
            dir_path = data_dir / dir_name
            if dir_path.exists():
                audio_files = list(dir_path.glob('*.wav'))
                for audio_file in audio_files:
                    data.append((str(audio_file), self.label_map[label]))
                print(f"从 {dir_name} 收集了 {len(audio_files)} 个文件")
        
        return data
    
    def _augment_weak_classes(self, data):
        """针对弱势类别进行数据增强"""
        label_counts = Counter([item[1] for item in data])
        max_count = max(label_counts.values())
        
        augmented_data = list(data)  # 保留原始数据
        
        for weak_class in self.weak_classes:
            if weak_class in label_counts:
                weak_data = [item for item in data if item[1] == weak_class]
                current_count = label_counts[weak_class]
                target_count = min(max_count, current_count * 3)  # 最多增强3倍
                
                # 重复采样弱势类别
                repeat_times = target_count // current_count
                for _ in range(repeat_times - 1):  # -1因为原始数据已经包含
                    augmented_data.extend(weak_data)
                
                print(f"弱势类别 {weak_class} 增强: {current_count} → {len([x for x in augmented_data if x[1] == weak_class])}")
        
        return augmented_data
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        audio_path, label = self.data[idx]
        
        # 对弱势类别应用数据增强
        if label in self.weak_classes and self.augment_weak_classes:
            features = self._augment_audio_features(audio_path)
        else:
            features = self.feature_extractor.extract_enhanced_features(audio_path)
        
        return torch.FloatTensor(features), torch.LongTensor([label])[0]
    
    def _augment_audio_features(self, audio_path):
        """音频数据增强"""
        # 随机选择增强策略
        augment_type = np.random.choice(['original', 'time_stretch', 'pitch_shift', 'noise'])
        
        if augment_type == 'original':
            return self.feature_extractor.extract_enhanced_features(audio_path)
        
        # 加载音频
        audio, sr = librosa.load(audio_path, sr=16000)
        
        if augment_type == 'time_stretch':
            # 时间拉伸
            rate = np.random.uniform(0.8, 1.2)
            audio = librosa.effects.time_stretch(audio, rate=rate)
        elif augment_type == 'pitch_shift':
            # 音调变化
            n_steps = np.random.uniform(-2, 2)
            audio = librosa.effects.pitch_shift(audio, sr=sr, n_steps=n_steps)
        elif augment_type == 'noise':
            # 添加噪声
            noise_factor = np.random.uniform(0.005, 0.02)
            noise = np.random.normal(0, noise_factor, audio.shape)
            audio = audio + noise
        
        # 保存临时文件并提取特征
        temp_path = f"temp_augmented_{np.random.randint(10000)}.wav"
        import soundfile as sf
        sf.write(temp_path, audio, sr)

        try:
            features = self.feature_extractor.extract_enhanced_features(temp_path)
        finally:
            # 清理临时文件
            if Path(temp_path).exists():
                Path(temp_path).unlink()
        
        return features

def create_class_weighted_sampler(dataset):
    """创建类别加权采样器"""
    labels = [dataset[i][1].item() for i in range(len(dataset))]
    label_counts = Counter(labels)
    
    # 计算类别权重（弱势类别权重更高）
    total_samples = len(labels)
    class_weights = {}
    for label, count in label_counts.items():
        if label in [2, 4]:  # 弱势类别
            class_weights[label] = total_samples / (count * 0.5)  # 增加权重
        else:
            class_weights[label] = total_samples / count
    
    # 为每个样本分配权重
    sample_weights = [class_weights[label] for label in labels]
    
    return WeightedRandomSampler(
        weights=sample_weights,
        num_samples=len(sample_weights),
        replacement=True
    )

def train_improved_model(model, train_loader, val_loader, num_epochs=150, device='cuda'):
    """改进的训练函数"""
    
    # 计算类别权重用于Focal Loss
    train_labels = []
    for _, labels in train_loader:
        train_labels.extend(labels.tolist())
    
    label_counts = Counter(train_labels)
    total_samples = len(train_labels)
    alpha_weights = []
    for i in range(5):
        if i in label_counts:
            weight = total_samples / (5 * label_counts[i])
            if i in [2, 4]:  # 弱势类别
                weight *= 2.0  # 额外增加权重
            alpha_weights.append(weight)
        else:
            alpha_weights.append(1.0)
    
    alpha_weights = torch.FloatTensor(alpha_weights).to(device)
    
    # 损失函数 - 使用Focal Loss
    criterion = FocalLoss(alpha=alpha_weights, gamma=2.0)
    
    # 优化器 - 使用AdamW
    optimizer = optim.AdamW(model.parameters(), lr=0.002, weight_decay=0.01)
    
    # 学习率调度器
    scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(optimizer, T_0=20, T_mult=2)
    
    # 训练历史
    history = {'train_loss': [], 'train_acc': [], 'val_loss': [], 'val_acc': []}
    
    best_val_acc = 0.0
    best_model_state = None
    patience_counter = 0
    early_stopping_patience = 40
    
    print(f"开始改进训练，设备: {device}")
    print(f"类别权重: {alpha_weights.cpu().numpy()}")
    
    for epoch in range(num_epochs):
        # 训练阶段
        model.train()
        train_loss = 0.0
        train_correct = 0
        train_total = 0
        
        train_pbar = tqdm(train_loader, desc=f'Epoch {epoch+1}/{num_epochs} [Train]')
        for features, labels in train_pbar:
            features, labels = features.to(device), labels.to(device)
            
            optimizer.zero_grad()
            outputs = model(features)
            loss = criterion(outputs, labels)
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()
            
            train_loss += loss.item()
            _, predicted = torch.max(outputs.data, 1)
            train_total += labels.size(0)
            train_correct += (predicted == labels).sum().item()
            
            train_pbar.set_postfix({
                'Loss': f'{loss.item():.4f}',
                'Acc': f'{100.*train_correct/train_total:.2f}%'
            })
        
        # 验证阶段
        model.eval()
        val_loss = 0.0
        val_correct = 0
        val_total = 0
        class_correct = [0] * 5
        class_total = [0] * 5
        
        with torch.no_grad():
            val_pbar = tqdm(val_loader, desc=f'Epoch {epoch+1}/{num_epochs} [Val]')
            for features, labels in val_pbar:
                features, labels = features.to(device), labels.to(device)
                
                outputs = model(features)
                loss = criterion(outputs, labels)
                
                val_loss += loss.item()
                _, predicted = torch.max(outputs, 1)
                val_total += labels.size(0)
                val_correct += (predicted == labels).sum().item()
                
                # 统计各类别准确率
                for i in range(labels.size(0)):
                    label = labels[i].item()
                    class_total[label] += 1
                    if predicted[i] == labels[i]:
                        class_correct[label] += 1
                
                val_pbar.set_postfix({
                    'Loss': f'{loss.item():.4f}',
                    'Acc': f'{100.*val_correct/val_total:.2f}%'
                })
        
        # 计算指标
        avg_train_loss = train_loss / len(train_loader)
        avg_train_acc = 100. * train_correct / train_total
        avg_val_loss = val_loss / len(val_loader)
        avg_val_acc = 100. * val_correct / val_total
        
        # 记录历史
        history['train_loss'].append(avg_train_loss)
        history['train_acc'].append(avg_train_acc)
        history['val_loss'].append(avg_val_loss)
        history['val_acc'].append(avg_val_acc)
        
        # 学习率调度
        scheduler.step()
        
        # 打印各类别准确率
        class_names = ['腹痛', '打嗝', '不适', '饥饿', '疲倦']
        class_accs = []
        for i in range(5):
            if class_total[i] > 0:
                acc = 100. * class_correct[i] / class_total[i]
                class_accs.append(acc)
                print(f"{class_names[i]}: {acc:.1f}%", end=" ")
            else:
                class_accs.append(0)
                print(f"{class_names[i]}: N/A", end=" ")
        print()
        
        # 保存最佳模型
        if avg_val_acc > best_val_acc:
            best_val_acc = avg_val_acc
            best_model_state = model.state_dict().copy()
            patience_counter = 0
            print(f"🎉 新的最佳验证准确率: {best_val_acc:.2f}%")
        else:
            patience_counter += 1
        
        print(f'Epoch {epoch+1}: Train Acc: {avg_train_acc:.2f}%, Val Acc: {avg_val_acc:.2f}%, Best: {best_val_acc:.2f}%')
        
        # 早停
        if patience_counter >= early_stopping_patience:
            print("早停触发")
            break
    
    # 加载最佳模型
    if best_model_state is not None:
        model.load_state_dict(best_model_state)
    
    return model, history

def main():
    """主函数"""
    print("🚀 改进的方案1训练")
    print("=" * 80)
    
    # 设备配置
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 数据目录
    data_dir = Path("Data")
    output_dir = Path("improved_solution1_results")
    output_dir.mkdir(exist_ok=True)
    
    # 创建改进的特征提取器
    print("\n🔧 创建改进的特征提取器...")
    feature_extractor = ImprovedFeatureExtractor()
    
    # 创建数据集
    print("\n📊 创建改进的数据集...")
    train_dataset = ImprovedInfantCryDataset(
        data_dir, feature_extractor, split='train', augment_weak_classes=True
    )
    val_dataset = ImprovedInfantCryDataset(
        data_dir, feature_extractor, split='test', augment_weak_classes=False
    )
    
    # 获取特征维度
    sample_features, _ = train_dataset[0]
    input_dim = sample_features.shape[0]
    print(f"特征维度: {input_dim}")
    
    # 创建数据加载器
    train_sampler = create_class_weighted_sampler(train_dataset)
    train_loader = DataLoader(train_dataset, batch_size=32, sampler=train_sampler)
    val_loader = DataLoader(val_dataset, batch_size=32, shuffle=False)
    
    # 创建改进的模型
    print(f"\n🏗️ 创建改进的模型...")
    model = ImprovedDeepInfantModel(input_dim=input_dim, num_classes=5)
    model = model.to(device)
    
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 训练模型
    print("\n🎯 开始改进训练...")
    trained_model, history = train_improved_model(model, train_loader, val_loader, num_epochs=150, device=device)
    
    # 保存模型
    model_path = output_dir / "improved_solution1_model.pth"
    torch.save(trained_model.state_dict(), model_path)
    print(f"✅ 模型已保存: {model_path}")
    
    # 保存训练历史
    with open(output_dir / "improved_solution1_history.json", 'w') as f:
        json.dump(history, f, indent=2)
    
    print(f"✅ 改进的方案1训练完成！")
    return trained_model, history

if __name__ == "__main__":
    main()
