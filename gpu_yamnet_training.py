#!/usr/bin/env python3
"""
GPU加速 + 真正YAMNet特征的高性能训练方案

这个方案使用您的GPU进行加速训练，并尝试使用真正的YAMNet预训练特征。

作者: DeepInfant Team
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader, WeightedRandomSampler
import torch.nn.functional as F
import librosa
import numpy as np
from pathlib import Path
import json
from collections import defaultdict, Counter
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, confusion_matrix
import matplotlib.pyplot as plt
import seaborn as sns
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

# 尝试导入TensorFlow和YAMNet
try:
    import tensorflow as tf
    import tensorflow_hub as hub
    # 设置TensorFlow使用CPU，避免GPU冲突
    tf.config.set_visible_devices([], 'GPU')
    YAMNET_AVAILABLE = True
    print("✅ TensorFlow Hub 可用，将使用真正的YAMNet特征")
except ImportError:
    YAMNET_AVAILABLE = False
    print("⚠️ TensorFlow Hub 不可用，将使用高级音频特征")

class YAMNetFeatureExtractor:
    """真正的YAMNet特征提取器"""
    
    def __init__(self):
        if YAMNET_AVAILABLE:
            try:
                print("🔄 加载YAMNet模型...")
                self.yamnet = hub.load('https://tfhub.dev/google/yamnet/1')
                self.available = True
                print("✅ YAMNet模型加载成功")
            except Exception as e:
                print(f"❌ YAMNet加载失败: {e}")
                self.available = False
        else:
            self.available = False
    
    def extract_features(self, audio_path, target_length=16000*3):
        """提取YAMNet特征或高级音频特征"""
        if self.available:
            return self._extract_yamnet_features(audio_path, target_length)
        else:
            return self._extract_advanced_features(audio_path, target_length)
    
    def _extract_yamnet_features(self, audio_path, target_length):
        """提取真正的YAMNet特征"""
        try:
            # 加载音频
            audio, sr = librosa.load(audio_path, sr=16000)
            
            # 调整长度
            if len(audio) < target_length:
                audio = np.pad(audio, (0, target_length - len(audio)), mode='constant')
            else:
                audio = audio[:target_length]
            
            # 转换为TensorFlow张量
            audio_tensor = tf.convert_to_tensor(audio, dtype=tf.float32)
            
            # 提取YAMNet特征
            _, embeddings, _ = self.yamnet(audio_tensor)
            
            # 转换为numpy并计算统计特征
            embeddings_np = embeddings.numpy()  # Shape: [num_frames, 1024]
            
            # 计算多种统计特征
            features = np.concatenate([
                np.mean(embeddings_np, axis=0),      # 1024维
                np.std(embeddings_np, axis=0),       # 1024维
                np.max(embeddings_np, axis=0),       # 1024维
                np.min(embeddings_np, axis=0),       # 1024维
                np.median(embeddings_np, axis=0),    # 1024维
            ])
            
            return features.astype(np.float32)  # 5120维特征
            
        except Exception as e:
            print(f"YAMNet特征提取失败: {e}")
            return self._extract_advanced_features(audio_path, target_length)
    
    def _extract_advanced_features(self, audio_path, target_length):
        """备用的高级音频特征提取"""
        try:
            # 加载音频
            audio, sr = librosa.load(audio_path, sr=16000)
            
            # 调整长度
            if len(audio) < target_length:
                audio = np.pad(audio, (0, target_length - len(audio)), mode='constant')
            else:
                audio = audio[:target_length]
            
            # 提取更丰富的特征
            features = []
            
            # 1. Mel频谱特征 (多个窗口大小)
            for n_fft in [512, 1024, 2048]:
                mel_spec = librosa.feature.melspectrogram(
                    y=audio, sr=sr, n_fft=n_fft, hop_length=n_fft//4, n_mels=64
                )
                mel_spec_db = librosa.power_to_db(mel_spec, ref=np.max)
                
                # 统计特征
                features.extend([
                    np.mean(mel_spec_db, axis=1),
                    np.std(mel_spec_db, axis=1),
                    np.max(mel_spec_db, axis=1),
                    np.min(mel_spec_db, axis=1)
                ])
            
            # 2. MFCC特征 (多个系数)
            for n_mfcc in [13, 20, 26]:
                mfcc = librosa.feature.mfcc(
                    y=audio, sr=sr, n_mfcc=n_mfcc, n_fft=1024, hop_length=512
                )
                features.extend([
                    np.mean(mfcc, axis=1),
                    np.std(mfcc, axis=1),
                    np.max(mfcc, axis=1),
                    np.min(mfcc, axis=1)
                ])
            
            # 3. 色度特征
            chroma = librosa.feature.chroma_stft(y=audio, sr=sr)
            features.extend([
                np.mean(chroma, axis=1),
                np.std(chroma, axis=1),
                np.max(chroma, axis=1),
                np.min(chroma, axis=1)
            ])
            
            # 4. 谱特征
            spectral_centroids = librosa.feature.spectral_centroid(y=audio, sr=sr)
            spectral_bandwidth = librosa.feature.spectral_bandwidth(y=audio, sr=sr)
            spectral_rolloff = librosa.feature.spectral_rolloff(y=audio, sr=sr)
            
            features.extend([
                np.mean(spectral_centroids), np.std(spectral_centroids),
                np.mean(spectral_bandwidth), np.std(spectral_bandwidth),
                np.mean(spectral_rolloff), np.std(spectral_rolloff)
            ])
            
            # 5. 时域特征
            zcr = librosa.feature.zero_crossing_rate(audio)
            rms = librosa.feature.rms(y=audio)
            
            features.extend([
                np.mean(zcr), np.std(zcr),
                np.mean(rms), np.std(rms)
            ])
            
            # 6. 全局统计特征
            features.extend([
                np.mean(audio), np.std(audio), np.max(audio), np.min(audio),
                np.sum(audio**2), len(audio)
            ])
            
            # 展平并返回
            flattened_features = []
            for f in features:
                if hasattr(f, 'flatten'):
                    flattened_features.append(f.flatten())
                elif isinstance(f, (int, float)):
                    flattened_features.append(np.array([f]))
                else:
                    flattened_features.append(np.array(f).flatten())

            features = np.concatenate(flattened_features)
            return features.astype(np.float32)
            
        except Exception as e:
            print(f"高级特征提取失败: {e}")
            return np.zeros(1000, dtype=np.float32)

class AdvancedDeepInfantModel(nn.Module):
    """高级DeepInfant模型 - 针对高维特征优化"""
    
    def __init__(self, input_dim, num_classes=5, dropout=0.3):
        super(AdvancedDeepInfantModel, self).__init__()
        
        # 根据输入维度设计网络
        if input_dim > 3000:  # YAMNet特征
            hidden_dims = [2048, 1024, 512, 256, 128]
        else:  # 高级音频特征
            hidden_dims = [512, 256, 128, 64, 32]
        
        # 特征提取器
        layers = []
        prev_dim = input_dim
        
        for i, hidden_dim in enumerate(hidden_dims):
            layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.BatchNorm1d(hidden_dim),
                nn.ReLU(inplace=True),
                nn.Dropout(dropout)
            ])
            prev_dim = hidden_dim
        
        self.feature_extractor = nn.Sequential(*layers)
        
        # 注意力机制
        self.attention = nn.Sequential(
            nn.Linear(prev_dim, prev_dim // 2),
            nn.Tanh(),
            nn.Linear(prev_dim // 2, 1),
            nn.Softmax(dim=1)
        )
        
        # 分类器
        self.classifier = nn.Sequential(
            nn.Linear(prev_dim, prev_dim // 2),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout / 2),
            nn.Linear(prev_dim // 2, num_classes)
        )
        
        self._initialize_weights()
    
    def _initialize_weights(self):
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.BatchNorm1d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
    
    def forward(self, x):
        # 特征提取
        features = self.feature_extractor(x)
        
        # 注意力权重
        attention_weights = self.attention(features)
        
        # 加权特征
        weighted_features = features * attention_weights
        
        # 分类
        output = self.classifier(weighted_features)
        return output

class InfantCryDataset(Dataset):
    """优化的婴儿哭声数据集"""
    
    def __init__(self, data_dir, feature_extractor, split='train', test_size=0.2, random_state=42):
        self.feature_extractor = feature_extractor
        self.label_map = {'bp': 0, 'bu': 1, 'dc': 2, 'hu': 3, 'ti': 4}
        self.label_names = {0: 'belly_pain', 1: 'burping', 2: 'discomfort', 3: 'hungry', 4: 'tired'}
        
        # 收集数据
        self.data = self._collect_data(data_dir)
        
        # 数据增强 - 对少数类别进行重复采样
        self.data = self._balance_data(self.data)
        
        # 划分数据集
        if len(self.data) > 0:
            train_data, test_data = train_test_split(
                self.data, test_size=test_size, random_state=random_state, 
                stratify=[item[1] for item in self.data]
            )
            
            if split == 'train':
                self.data = train_data
            else:
                self.data = test_data
        
        print(f"{split} 集大小: {len(self.data)}")
        if len(self.data) > 0:
            label_counts = Counter([item[1] for item in self.data])
            print(f"标签分布: {dict(label_counts)}")
    
    def _collect_data(self, data_dir):
        """收集数据文件"""
        data = []
        data_dir = Path(data_dir)
        
        dir_to_label = {
            'belly_pain': 'bp', 'burping': 'bu', 'discomfort': 'dc',
            'hungry': 'hu', 'tired': 'ti'
        }
        
        for dir_name, label in dir_to_label.items():
            dir_path = data_dir / dir_name
            if dir_path.exists():
                audio_files = list(dir_path.glob('*.wav'))
                for audio_file in audio_files:
                    data.append((str(audio_file), self.label_map[label]))
                print(f"从 {dir_name} 收集了 {len(audio_files)} 个文件")
        
        return data
    
    def _balance_data(self, data):
        """平衡数据 - 对少数类别进行重复采样"""
        label_counts = Counter([item[1] for item in data])
        max_count = max(label_counts.values())
        
        balanced_data = []
        for label, count in label_counts.items():
            label_data = [item for item in data if item[1] == label]
            
            # 计算需要重复的次数
            repeat_times = max(1, max_count // (count * 2))  # 不要过度重复
            
            for _ in range(repeat_times):
                balanced_data.extend(label_data)
        
        print(f"数据平衡后: {len(data)} -> {len(balanced_data)}")
        return balanced_data
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        audio_path, label = self.data[idx]
        features = self.feature_extractor.extract_features(audio_path)
        return torch.FloatTensor(features), torch.LongTensor([label])[0]

def train_advanced_model(model, train_loader, val_loader, num_epochs=200, device='cuda'):
    """高级训练函数"""
    
    # 损失函数 - 使用标签平滑
    class LabelSmoothingCrossEntropy(nn.Module):
        def __init__(self, smoothing=0.1):
            super().__init__()
            self.smoothing = smoothing
        
        def forward(self, pred, target):
            n_class = pred.size(1)
            one_hot = torch.zeros_like(pred).scatter(1, target.unsqueeze(1), 1)
            one_hot = one_hot * (1 - self.smoothing) + self.smoothing / n_class
            log_prob = F.log_softmax(pred, dim=1)
            return F.kl_div(log_prob, one_hot, reduction='batchmean')
    
    criterion = LabelSmoothingCrossEntropy(smoothing=0.1)
    
    # 优化器 - 使用更激进的学习率
    optimizer = optim.AdamW(model.parameters(), lr=0.003, weight_decay=0.01)
    
    # 学习率调度器
    scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(optimizer, T_0=10, T_mult=2)
    
    # 训练历史
    history = {'train_loss': [], 'train_acc': [], 'val_loss': [], 'val_acc': []}
    
    best_val_acc = 0.0
    best_model_state = None
    patience_counter = 0
    early_stopping_patience = 30
    
    print(f"开始高级训练，设备: {device}")
    
    for epoch in range(num_epochs):
        # 训练阶段
        model.train()
        train_loss = 0.0
        train_correct = 0
        train_total = 0
        
        train_pbar = tqdm(train_loader, desc=f'Epoch {epoch+1}/{num_epochs} [Train]')
        for features, labels in train_pbar:
            features, labels = features.to(device), labels.to(device)
            
            optimizer.zero_grad()
            outputs = model(features)
            loss = criterion(outputs, labels)
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            
            optimizer.step()
            
            train_loss += loss.item()
            _, predicted = torch.max(outputs.data, 1)
            train_total += labels.size(0)
            train_correct += (predicted == labels).sum().item()
            
            train_pbar.set_postfix({
                'Loss': f'{loss.item():.4f}',
                'Acc': f'{100.*train_correct/train_total:.2f}%'
            })
        
        # 验证阶段
        model.eval()
        val_loss = 0.0
        val_correct = 0
        val_total = 0
        
        with torch.no_grad():
            val_pbar = tqdm(val_loader, desc=f'Epoch {epoch+1}/{num_epochs} [Val]')
            for features, labels in val_pbar:
                features, labels = features.to(device), labels.to(device)
                outputs = model(features)
                loss = criterion(outputs, labels)
                
                val_loss += loss.item()
                _, predicted = torch.max(outputs.data, 1)
                val_total += labels.size(0)
                val_correct += (predicted == labels).sum().item()
                
                val_pbar.set_postfix({
                    'Loss': f'{loss.item():.4f}',
                    'Acc': f'{100.*val_correct/val_total:.2f}%'
                })
        
        # 计算平均指标
        avg_train_loss = train_loss / len(train_loader)
        avg_train_acc = 100. * train_correct / train_total
        avg_val_loss = val_loss / len(val_loader)
        avg_val_acc = 100. * val_correct / val_total
        
        # 记录历史
        history['train_loss'].append(avg_train_loss)
        history['train_acc'].append(avg_train_acc)
        history['val_loss'].append(avg_val_loss)
        history['val_acc'].append(avg_val_acc)
        
        # 学习率调度
        scheduler.step()
        
        # 保存最佳模型
        if avg_val_acc > best_val_acc:
            best_val_acc = avg_val_acc
            best_model_state = model.state_dict().copy()
            patience_counter = 0
        else:
            patience_counter += 1
        
        print(f'Epoch {epoch+1}/{num_epochs}:')
        print(f'  Train Loss: {avg_train_loss:.4f}, Train Acc: {avg_train_acc:.2f}%')
        print(f'  Val Loss: {avg_val_loss:.4f}, Val Acc: {avg_val_acc:.2f}%')
        print(f'  Best Val Acc: {best_val_acc:.2f}%')
        print(f'  LR: {optimizer.param_groups[0]["lr"]:.6f}')
        print('-' * 50)
        
        # 早停检查
        if patience_counter >= early_stopping_patience:
            print(f"早停触发！验证准确率在 {early_stopping_patience} 个 epoch 内没有改善。")
            break
    
    # 加载最佳模型
    if best_model_state is not None:
        model.load_state_dict(best_model_state)
    
    return model, history

def main():
    print("🚀 GPU加速 + YAMNet特征的高性能训练")
    print("=" * 60)
    
    # GPU检查和设置
    if torch.cuda.is_available():
        device = torch.device('cuda')
        print(f"✅ 使用GPU: {torch.cuda.get_device_name()}")
        print(f"   GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
    else:
        device = torch.device('cpu')
        print("⚠️ GPU不可用，使用CPU")
    
    # 创建输出目录
    output_dir = Path("gpu_yamnet_training_results")
    output_dir.mkdir(exist_ok=True)
    
    # 初始化特征提取器
    print("\n📥 初始化特征提取器...")
    feature_extractor = YAMNetFeatureExtractor()
    
    # 创建数据集
    print("\n📊 创建数据集...")
    train_dataset = InfantCryDataset('Data', feature_extractor, split='train')
    val_dataset = InfantCryDataset('Data', feature_extractor, split='test')
    
    if len(train_dataset) == 0 or len(val_dataset) == 0:
        print("❌ 数据集为空")
        return
    
    # 获取特征维度
    sample_features, _ = train_dataset[0]
    input_dim = sample_features.shape[0]
    print(f"特征维度: {input_dim}")
    
    # 创建数据加载器
    train_loader = DataLoader(train_dataset, batch_size=32, shuffle=True, num_workers=4)
    val_loader = DataLoader(val_dataset, batch_size=32, shuffle=False, num_workers=4)
    
    # 创建模型
    print(f"\n🏗️ 创建高级模型...")
    model = AdvancedDeepInfantModel(input_dim=input_dim, num_classes=5)
    model = model.to(device)
    
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 训练模型
    print("\n🎯 开始高级训练...")
    trained_model, history = train_advanced_model(model, train_loader, val_loader, num_epochs=200, device=device)
    
    # 保存模型
    model_path = output_dir / "gpu_yamnet_model.pth"
    torch.save(trained_model.state_dict(), model_path)
    
    full_model_path = output_dir / "gpu_yamnet_full_model.pth"
    torch.save(trained_model, full_model_path)
    
    print(f"✅ 模型已保存: {model_path}")
    
    # 保存结果
    results = {
        'training_history': history,
        'model_config': {
            'input_dim': input_dim,
            'num_classes': 5,
            'feature_extractor': 'YAMNet' if feature_extractor.available else 'Advanced',
            'device': str(device)
        }
    }
    
    results_path = output_dir / "training_results.json"
    with open(results_path, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"📁 所有输出文件保存在: {output_dir}")
    
    # 显示最终结果
    final_val_acc = max(history['val_acc'])
    print(f"\n🎯 训练完成！最佳验证准确率: {final_val_acc:.2f}%")

if __name__ == "__main__":
    main()
