#!/usr/bin/env python3
"""
测试方案1的性能

这个脚本会测试方案1训练的模型在各个类别上的识别准确率。

作者: DeepInfant Team
"""

import torch
import torch.nn as nn
import librosa
import numpy as np
from pathlib import Path
import json
from collections import defaultdict, Counter
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

class StableFeatureExtractor:
    """稳定的音频特征提取器 (与训练时保持一致)"""
    
    def __init__(self):
        self.sr = 16000
        
    def extract_features(self, audio_path, target_length=16000*3):
        """提取稳定的音频特征"""
        try:
            # 加载音频
            audio, sr = librosa.load(audio_path, sr=self.sr)
            
            # 调整长度
            if len(audio) < target_length:
                audio = np.pad(audio, (0, target_length - len(audio)), mode='constant')
            else:
                audio = audio[:target_length]
            
            features = []
            
            # 1. Mel频谱特征
            mel_spec = librosa.feature.melspectrogram(
                y=audio, sr=sr, n_mels=128, n_fft=1024, hop_length=512
            )
            mel_spec_db = librosa.power_to_db(mel_spec, ref=np.max)
            
            # 统计特征
            features.extend([
                np.mean(mel_spec_db, axis=1),  # 128维
                np.std(mel_spec_db, axis=1),   # 128维
                np.max(mel_spec_db, axis=1),   # 128维
                np.min(mel_spec_db, axis=1),   # 128维
            ])
            
            # 2. MFCC特征
            mfcc = librosa.feature.mfcc(y=audio, sr=sr, n_mfcc=20)
            features.extend([
                np.mean(mfcc, axis=1),  # 20维
                np.std(mfcc, axis=1),   # 20维
                np.max(mfcc, axis=1),   # 20维
                np.min(mfcc, axis=1),   # 20维
            ])
            
            # 3. 色度特征
            chroma = librosa.feature.chroma_stft(y=audio, sr=sr)
            features.extend([
                np.mean(chroma, axis=1),  # 12维
                np.std(chroma, axis=1),   # 12维
            ])
            
            # 4. 谱特征
            spectral_centroids = librosa.feature.spectral_centroid(y=audio, sr=sr)
            spectral_bandwidth = librosa.feature.spectral_bandwidth(y=audio, sr=sr)
            spectral_rolloff = librosa.feature.spectral_rolloff(y=audio, sr=sr)
            
            features.extend([
                np.mean(spectral_centroids),
                np.std(spectral_centroids),
                np.mean(spectral_bandwidth),
                np.std(spectral_bandwidth),
                np.mean(spectral_rolloff),
                np.std(spectral_rolloff)
            ])
            
            # 5. 时域特征
            zcr = librosa.feature.zero_crossing_rate(audio)
            rms = librosa.feature.rms(y=audio)
            
            features.extend([
                np.mean(zcr),
                np.std(zcr),
                np.mean(rms),
                np.std(rms)
            ])
            
            # 6. 全局统计特征
            features.extend([
                np.mean(audio),
                np.std(audio),
                np.max(audio),
                np.min(audio),
                np.sum(audio**2),
                len(audio)
            ])
            
            # 安全地展平所有特征
            final_features = []
            for f in features:
                if isinstance(f, np.ndarray):
                    final_features.extend(f.flatten())
                elif isinstance(f, (int, float, np.integer, np.floating)):
                    final_features.append(float(f))
                else:
                    final_features.extend(np.array(f).flatten())
            
            return np.array(final_features, dtype=np.float32)
            
        except Exception as e:
            print(f"特征提取失败 {audio_path}: {e}")
            return np.zeros(600, dtype=np.float32)

class StableDeepInfantModel(nn.Module):
    """稳定的DeepInfant模型 (与训练时保持一致)"""

    def __init__(self, input_dim, num_classes=5, dropout=0.4):
        super(StableDeepInfantModel, self).__init__()

        # 特征提取器
        self.feature_extractor = nn.Sequential(
            nn.Linear(input_dim, 1024),
            nn.BatchNorm1d(1024),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout),

            nn.Linear(1024, 512),
            nn.BatchNorm1d(512),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout),

            nn.Linear(512, 256),
            nn.BatchNorm1d(256),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout),
        )

        # 分类器
        self.classifier = nn.Sequential(
            nn.Linear(256, 128),
            nn.BatchNorm1d(128),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout / 2),

            nn.Linear(128, num_classes)
        )

        self._initialize_weights()

    def _initialize_weights(self):
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.BatchNorm1d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)

    def forward(self, x):
        features = self.feature_extractor(x)
        output = self.classifier(features)
        return output

class StableV1Predictor:
    """方案1模型预测器"""
    
    def __init__(self, model_path, device='cpu'):
        self.device = device
        self.feature_extractor = StableFeatureExtractor()
        
        # 标签映射
        self.label_map = {0: 'bp', 1: 'bu', 2: 'dc', 3: 'hu', 4: 'ti'}
        self.label_names = {
            'bp': 'belly_pain (腹痛)',
            'bu': 'burping (打嗝)',
            'dc': 'discomfort (不适)',
            'hu': 'hungry (饥饿)',
            'ti': 'tired (疲倦)'
        }
        
        # 加载模型
        try:
            self.model = torch.load(model_path, map_location=device)
            self.model.eval()
            print(f"✅ 成功加载方案1模型: {Path(model_path).name}")
        except Exception as e:
            print(f"❌ 模型加载失败: {e}")
            self.model = None
    
    def predict(self, audio_path):
        """预测单个音频文件"""
        if self.model is None:
            return None, 0, 0
        
        # 提取特征
        features = self.feature_extractor.extract_features(audio_path)
        if features is None:
            return None, 0, 0
        
        # 转换为张量
        features_tensor = torch.FloatTensor(features).unsqueeze(0).to(self.device)
        
        # 预测
        with torch.no_grad():
            outputs = self.model(features_tensor)
            probabilities = torch.softmax(outputs, dim=1)[0]
            predicted_class_idx = torch.argmax(probabilities).item()
            confidence = probabilities[predicted_class_idx].item()
        
        predicted_label = self.label_map[predicted_class_idx]
        return predicted_label, confidence, 0.001  # 假设推理时间
    
    def predict_batch(self, directory):
        """批量预测目录中的音频文件"""
        directory = Path(directory)
        if not directory.exists():
            return []
        
        # 支持的音频格式
        audio_extensions = {'.wav', '.mp3', '.m4a', '.flac', '.ogg'}
        audio_files = [f for f in directory.rglob('*') if f.suffix.lower() in audio_extensions]
        
        if not audio_files:
            print(f"在 {directory} 中未找到音频文件")
            return []
        
        print(f"在 {directory} 中找到 {len(audio_files)} 个音频文件")
        
        results = []
        for audio_file in tqdm(audio_files, desc=f"处理 {directory.name}"):
            predicted_label, confidence, inference_time = self.predict(audio_file)
            
            if predicted_label is not None:
                results.append({
                    'filename': audio_file.name,
                    'filepath': str(audio_file),
                    'predicted_label': predicted_label,
                    'predicted_name': self.label_names[predicted_label],
                    'confidence': float(confidence),
                    'inference_time': inference_time
                })
            else:
                results.append({
                    'filename': audio_file.name,
                    'filepath': str(audio_file),
                    'error': 'prediction_failed'
                })
        
        return results

def extract_true_label_from_filename(filename, directory_name=None):
    """从文件名或目录名提取真实标签"""
    filename_lower = filename.lower()
    
    # 目录名映射
    dir_mapping = {
        'belly_pain': 'bp',
        'burping': 'bu', 
        'discomfort': 'dc',
        'hungry': 'hu',
        'tired': 'ti'
    }
    
    # 文件名映射
    filename_mapping = {
        'bp': 'bp', 'belly': 'bp', 'pain': 'bp',
        'bu': 'bu', 'burp': 'bu', 'burping': 'bu',
        'dc': 'dc', 'discomfort': 'dc', 'uncomfortable': 'dc',
        'hu': 'hu', 'hungry': 'hu', 'hunger': 'hu',
        'ti': 'ti', 'tired': 'ti', 'sleepy': 'ti', 'sleep': 'ti'
    }
    
    # 首先尝试从目录名提取
    if directory_name:
        for dir_key, label in dir_mapping.items():
            if dir_key in directory_name.lower():
                return label
    
    # 然后尝试从文件名提取
    for key, label in filename_mapping.items():
        if key in filename_lower:
            return label
    
    return None

def calculate_accuracy(results, directory_name=None):
    """计算准确率"""
    correct = 0
    total = 0
    label_stats = defaultdict(lambda: {'correct': 0, 'total': 0, 'predictions': defaultdict(int)})
    
    for result in results:
        if 'error' in result:
            continue
        
        filename = result['filename']
        predicted = result['predicted_label']
        
        # 提取真实标签
        true_label = extract_true_label_from_filename(filename, directory_name)
        
        if true_label:
            total += 1
            label_stats[true_label]['total'] += 1
            label_stats[true_label]['predictions'][predicted] += 1
            
            if predicted == true_label:
                correct += 1
                label_stats[true_label]['correct'] += 1
    
    overall_accuracy = correct / total if total > 0 else 0
    return overall_accuracy, label_stats, total

def test_stable_v1_model():
    """测试方案1模型"""
    print("🧪 测试方案1: GPU加速稳定训练模型")
    print("=" * 60)
    
    # 设备选择
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 加载训练后的模型
    model_path = "stable_training_v1_results/stable_v1_full_model.pth"
    if not Path(model_path).exists():
        print(f"❌ 模型文件不存在: {model_path}")
        return
    
    predictor = StableV1Predictor(model_path, device)
    
    # 测试数据目录
    test_dirs = [
        'Data/belly_pain',
        'Data/burping', 
        'Data/discomfort',
        'Data/hungry',
        'Data/tired'
    ]
    
    all_results = []
    
    print(f"\n{'='*60}")
    print("🔄 开始测试...")
    
    for test_dir in test_dirs:
        if Path(test_dir).exists():
            print(f"\n📁 测试目录: {test_dir}")
            results = predictor.predict_batch(test_dir)
            
            # 为每个结果添加目录信息
            dir_name = Path(test_dir).name
            for result in results:
                result['directory'] = dir_name
            
            all_results.extend(results)
            
            # 显示一些示例结果
            valid_results = [r for r in results if 'error' not in r]
            if valid_results:
                print(f"   处理了 {len(valid_results)} 个文件")
                for i, result in enumerate(valid_results[:3]):
                    print(f"   示例 {i+1}: {result['filename']} -> {result['predicted_name']} "
                          f"(置信度: {result['confidence']:.3f})")
            else:
                print(f"   ⚠️ 该目录中没有有效的预测结果")
    
    # 计算总体性能
    if all_results:
        print(f"\n📊 性能分析:")
        
        # 按目录分别计算准确率
        dir_accuracies = {}
        total_correct = 0
        total_samples = 0
        
        for test_dir in test_dirs:
            dir_name = Path(test_dir).name
            dir_results = [r for r in all_results if r.get('directory') == dir_name and 'error' not in r]
            
            if dir_results:
                accuracy, label_stats, samples = calculate_accuracy(dir_results, dir_name)
                dir_accuracies[dir_name] = {
                    'accuracy': accuracy,
                    'samples': samples,
                    'label_stats': label_stats
                }
                
                print(f"\n   📂 {dir_name}:")
                print(f"      样本数: {samples}")
                print(f"      准确率: {accuracy:.3f} ({accuracy*100:.1f}%)")
                
                # 显示各类别的详细统计
                for true_label, stats in label_stats.items():
                    if stats['total'] > 0:
                        class_accuracy = stats['correct'] / stats['total']
                        print(f"      {predictor.label_names[true_label]}: "
                              f"{stats['correct']}/{stats['total']} "
                              f"({class_accuracy:.3f})")
                        
                        # 显示预测分布
                        if len(stats['predictions']) > 1:
                            pred_dist = ", ".join([f"{predictor.label_names[pred]}:{count}" 
                                                 for pred, count in stats['predictions'].items()])
                            print(f"         预测分布: {pred_dist}")
                
                total_correct += sum(stats['correct'] for stats in label_stats.values())
                total_samples += samples
        
        # 计算总体准确率
        overall_accuracy = total_correct / total_samples if total_samples > 0 else 0
        
        # 计算平均推理时间
        inference_times = [r['inference_time'] for r in all_results if 'inference_time' in r]
        avg_inference_time = np.mean(inference_times) if inference_times else 0
        
        print(f"\n🎯 方案1总体性能:")
        print(f"   总体准确率: {overall_accuracy:.3f} ({overall_accuracy*100:.1f}%)")
        print(f"   总样本数: {total_samples}")
        print(f"   平均推理时间: {avg_inference_time:.3f}s")
        
        # 与之前的结果对比
        print(f"\n📈 性能对比:")
        print(f"   原始模型准确率: 13.1%")
        print(f"   简化训练准确率: 38.9%")
        print(f"   方案1准确率: {overall_accuracy*100:.1f}%")
        improvement_from_original = (overall_accuracy * 100) - 13.1
        improvement_from_simple = (overall_accuracy * 100) - 38.9
        print(f"   相比原始提升: {improvement_from_original:+.1f} 个百分点")
        print(f"   相比简化训练提升: {improvement_from_simple:+.1f} 个百分点")
        
        # 保存详细结果
        results_summary = {
            'method': 'Stable V1 Training',
            'overall_accuracy': overall_accuracy,
            'total_samples': total_samples,
            'avg_inference_time': avg_inference_time,
            'dir_accuracies': dir_accuracies,
            'improvement_over_original': improvement_from_original,
            'improvement_over_simple': improvement_from_simple,
            'detailed_results': all_results
        }
        
        report_path = Path("stable_v1_performance_report.json")
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(results_summary, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"\n📋 详细性能报告已保存: {report_path}")
        
        return results_summary
    else:
        print("❌ 没有获得任何测试结果")
        return None

def main():
    print("🚀 方案1性能测试")
    print("=" * 60)
    print("测试GPU加速稳定训练模型的性能")
    print("=" * 60)
    
    # 运行测试
    results = test_stable_v1_model()
    
    # 显示最终总结
    if results:
        print(f"\n{'='*60}")
        print("🏆 方案1最终性能总结")
        print("=" * 60)
        
        print(f"📊 方案1模型:")
        print(f"   总体准确率: {results['overall_accuracy']:.3f} ({results['overall_accuracy']*100:.1f}%)")
        print(f"   测试样本数: {results['total_samples']}")
        print(f"   平均推理时间: {results['avg_inference_time']:.3f}s")
        print(f"   相比原始模型提升: {results['improvement_over_original']:+.1f} 个百分点")
        print(f"   相比简化训练提升: {results['improvement_over_simple']:+.1f} 个百分点")
        
        # 显示各类别的准确率
        print(f"\n   各类别准确率:")
        for dir_name, dir_result in results['dir_accuracies'].items():
            print(f"     {dir_name}: {dir_result['accuracy']:.3f} ({dir_result['samples']} 样本)")
        
        print(f"\n🎉 方案1测试完成！")
    else:
        print("❌ 测试失败")

if __name__ == "__main__":
    main()
