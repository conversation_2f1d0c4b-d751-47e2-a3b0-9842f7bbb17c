#!/usr/bin/env python3
"""
测试精确架构模型的分类性能

这个脚本会加载新创建的精确架构模型，并在现有测试数据上评估其性能。

作者: DeepInfant Team
"""

import torch
import torch.nn as nn
import numpy as np
import librosa
from pathlib import Path
import json
from collections import defaultdict
import time

class ONNXPreciseModelPredictor:
    def __init__(self, model_path):
        """使用 ONNX Runtime 加载精确模型"""
        try:
            import onnxruntime as ort
            self.session = ort.InferenceSession(model_path)
            self.model_name = Path(model_path).stem
            print(f"✅ 成功加载 ONNX 模型: {self.model_name}")
        except ImportError:
            raise ImportError("请安装 onnxruntime: pip install onnxruntime")
        
        # 标签映射
        self.label_map = {
            0: 'bp',  # belly pain
            1: 'bu',  # burping
            2: 'ch',  # cold/hot
            3: 'dc',  # discomfort
            4: 'hu',  # hungry
            5: 'lo',  # lonely
            6: 'sc',  # scared
            7: 'ti',  # tired
            8: 'un',  # unknown
        }
        
        self.label_names = {
            'bp': 'belly pain (腹痛)',
            'bu': 'burping (打嗝)',
            'ch': 'cold/hot (冷热)',
            'dc': 'discomfort (不适)',
            'hu': 'hungry (饥饿)',
            'lo': 'lonely (孤独)',
            'sc': 'scared (害怕)',
            'ti': 'tired (疲倦)',
            'un': 'unknown (未知)'
        }
    
    def _process_audio(self, audio_path):
        """处理音频文件为模型输入格式"""
        try:
            # 加载音频
            audio, sr = librosa.load(audio_path, sr=16000, duration=7.0)
            
            # 如果音频太短，进行填充
            target_length = 16000 * 7  # 7秒
            if len(audio) < target_length:
                audio = np.pad(audio, (0, target_length - len(audio)), mode='constant')
            else:
                audio = audio[:target_length]
            
            # 生成 mel 频谱图
            mel_spec = librosa.feature.melspectrogram(
                y=audio,
                sr=sr,
                n_fft=1024,
                hop_length=256,
                n_mels=80,
                fmin=20,
                fmax=8000
            )
            
            # 转换为对数刻度
            mel_spec = librosa.power_to_db(mel_spec, ref=np.max)
            
            # 归一化到 [-1, 1]
            mel_spec = (mel_spec - mel_spec.mean()) / (mel_spec.std() + 1e-8)
            
            # 调整到目标尺寸 [80, 432]
            if mel_spec.shape[1] != 432:
                if mel_spec.shape[1] > 432:
                    mel_spec = mel_spec[:, :432]
                else:
                    # 填充到 432
                    pad_width = 432 - mel_spec.shape[1]
                    mel_spec = np.pad(mel_spec, ((0, 0), (0, pad_width)), mode='constant')
            
            # 添加 batch 和 channel 维度
            mel_spec = np.expand_dims(mel_spec, axis=0)  # channel 维度
            mel_spec = np.expand_dims(mel_spec, axis=0)  # batch 维度
            
            return mel_spec.astype(np.float32)
            
        except Exception as e:
            print(f"处理音频文件 {audio_path} 时出错: {e}")
            return None
    
    def predict(self, audio_path):
        """预测单个音频文件"""
        # 处理音频
        input_data = self._process_audio(audio_path)
        if input_data is None:
            return None, 0, 0
        
        # 获取模型输入名称
        input_name = self.session.get_inputs()[0].name
        
        # 运行推理
        start_time = time.time()
        try:
            outputs = self.session.run(None, {input_name: input_data})
            inference_time = time.time() - start_time
            
            probabilities = outputs[0][0]  # 移除 batch 维度
            
            # 获取预测结果
            predicted_class_idx = np.argmax(probabilities)
            confidence = probabilities[predicted_class_idx]
            predicted_label = self.label_map[predicted_class_idx]
            
            return predicted_label, confidence, inference_time
            
        except Exception as e:
            print(f"推理时出错: {e}")
            return None, 0, 0
    
    def predict_batch(self, directory):
        """批量预测目录中的音频文件"""
        directory = Path(directory)
        if not directory.exists():
            return []
        
        # 支持的音频格式
        audio_extensions = {'.wav', '.mp3', '.m4a', '.flac', '.ogg'}
        audio_files = [f for f in directory.rglob('*') if f.suffix.lower() in audio_extensions]
        
        if not audio_files:
            print(f"在 {directory} 中未找到音频文件")
            return []
        
        print(f"在 {directory} 中找到 {len(audio_files)} 个音频文件")
        
        results = []
        for audio_file in audio_files:
            predicted_label, confidence, inference_time = self.predict(audio_file)
            
            if predicted_label is not None:
                results.append({
                    'filename': audio_file.name,
                    'filepath': str(audio_file),
                    'predicted_label': predicted_label,
                    'predicted_name': self.label_names[predicted_label],
                    'confidence': float(confidence),
                    'inference_time': inference_time
                })
            else:
                results.append({
                    'filename': audio_file.name,
                    'filepath': str(audio_file),
                    'error': 'prediction_failed'
                })
        
        return results

def extract_true_label_from_filename(filename, directory_name=None):
    """从文件名或目录名提取真实标签"""
    filename_lower = filename.lower()
    
    # 目录名映射
    dir_mapping = {
        'belly_pain': 'bp',
        'burping': 'bu', 
        'discomfort': 'dc',
        'hungry': 'hu',
        'tired': 'ti'
    }
    
    # 文件名映射
    filename_mapping = {
        'bp': 'bp', 'belly': 'bp', 'pain': 'bp',
        'bu': 'bu', 'burp': 'bu', 'burping': 'bu',
        'dc': 'dc', 'discomfort': 'dc', 'uncomfortable': 'dc',
        'hu': 'hu', 'hungry': 'hu', 'hunger': 'hu',
        'ti': 'ti', 'tired': 'ti', 'sleepy': 'ti', 'sleep': 'ti'
    }
    
    # 首先尝试从目录名提取
    if directory_name:
        for dir_key, label in dir_mapping.items():
            if dir_key in directory_name.lower():
                return label
    
    # 然后尝试从文件名提取
    for key, label in filename_mapping.items():
        if key in filename_lower:
            return label
    
    return None

def calculate_accuracy(results, directory_name=None):
    """计算准确率"""
    correct = 0
    total = 0
    label_stats = defaultdict(lambda: {'correct': 0, 'total': 0, 'predictions': defaultdict(int)})
    
    for result in results:
        if 'error' in result:
            continue
        
        filename = result['filename']
        predicted = result['predicted_label']
        
        # 提取真实标签
        true_label = extract_true_label_from_filename(filename, directory_name)
        
        if true_label:
            total += 1
            label_stats[true_label]['total'] += 1
            label_stats[true_label]['predictions'][predicted] += 1
            
            if predicted == true_label:
                correct += 1
                label_stats[true_label]['correct'] += 1
    
    overall_accuracy = correct / total if total > 0 else 0
    return overall_accuracy, label_stats, total

def test_precise_models():
    """测试精确架构模型"""
    print("🚀 测试精确架构模型的分类性能")
    print("=" * 60)
    
    # 要测试的模型
    models_to_test = [
        'precise_architecture_models/DeepInfant_VGGish_Precise.onnx',
        'precise_architecture_models/DeepInfant_AFP_Simple.onnx'
    ]
    
    # 测试数据目录
    test_dirs = [
        'processed_dataset/test',
        'Data/belly_pain',
        'Data/burping', 
        'Data/discomfort',
        'Data/hungry',
        'Data/tired'
    ]
    
    all_results = {}
    
    for model_path in models_to_test:
        if not Path(model_path).exists():
            print(f"❌ 模型文件不存在: {model_path}")
            continue
        
        print(f"\n{'='*60}")
        print(f"🔄 测试模型: {Path(model_path).name}")
        print('='*60)
        
        try:
            predictor = ONNXPreciseModelPredictor(model_path)
            model_results = []
            
            for test_dir in test_dirs:
                if Path(test_dir).exists():
                    print(f"\n📁 测试目录: {test_dir}")
                    results = predictor.predict_batch(test_dir)
                    
                    # 为每个结果添加目录信息
                    dir_name = Path(test_dir).name
                    for result in results:
                        result['directory'] = dir_name
                    
                    model_results.extend(results)
                    
                    # 显示一些示例结果
                    valid_results = [r for r in results if 'error' not in r]
                    if valid_results:
                        print(f"   处理了 {len(valid_results)} 个文件")
                        for i, result in enumerate(valid_results[:3]):
                            print(f"   示例 {i+1}: {result['filename']} -> {result['predicted_name']} "
                                  f"(置信度: {result['confidence']:.3f})")
                    else:
                        print(f"   ⚠️ 该目录中没有有效的预测结果")
            
            # 计算总体性能
            if model_results:
                print(f"\n📊 性能分析:")
                
                # 按目录分别计算准确率
                dir_accuracies = {}
                total_correct = 0
                total_samples = 0
                
                for test_dir in test_dirs:
                    dir_name = Path(test_dir).name
                    dir_results = [r for r in model_results if r.get('directory') == dir_name and 'error' not in r]
                    
                    if dir_results:
                        accuracy, label_stats, samples = calculate_accuracy(dir_results, dir_name)
                        dir_accuracies[dir_name] = {
                            'accuracy': accuracy,
                            'samples': samples,
                            'label_stats': label_stats
                        }
                        
                        print(f"\n   📂 {dir_name}:")
                        print(f"      样本数: {samples}")
                        print(f"      准确率: {accuracy:.3f} ({accuracy*100:.1f}%)")
                        
                        # 显示各类别的详细统计
                        for true_label, stats in label_stats.items():
                            if stats['total'] > 0:
                                class_accuracy = stats['correct'] / stats['total']
                                print(f"      {predictor.label_names[true_label]}: "
                                      f"{stats['correct']}/{stats['total']} "
                                      f"({class_accuracy:.3f})")
                                
                                # 显示预测分布
                                if len(stats['predictions']) > 1:
                                    pred_dist = ", ".join([f"{predictor.label_names[pred]}:{count}" 
                                                         for pred, count in stats['predictions'].items()])
                                    print(f"         预测分布: {pred_dist}")
                        
                        total_correct += sum(stats['correct'] for stats in label_stats.values())
                        total_samples += samples
                
                # 计算总体准确率
                overall_accuracy = total_correct / total_samples if total_samples > 0 else 0
                
                # 计算平均推理时间
                inference_times = [r['inference_time'] for r in model_results if 'inference_time' in r]
                avg_inference_time = np.mean(inference_times) if inference_times else 0
                
                print(f"\n🎯 总体性能:")
                print(f"   总体准确率: {overall_accuracy:.3f} ({overall_accuracy*100:.1f}%)")
                print(f"   总样本数: {total_samples}")
                print(f"   平均推理时间: {avg_inference_time:.3f}s")
                
                all_results[Path(model_path).name] = {
                    'overall_accuracy': overall_accuracy,
                    'total_samples': total_samples,
                    'avg_inference_time': avg_inference_time,
                    'dir_accuracies': dir_accuracies,
                    'detailed_results': model_results
                }
            else:
                print("❌ 没有有效的测试结果")
                
        except Exception as e:
            print(f"❌ 测试模型时出错: {e}")
    
    # 保存详细结果
    if all_results:
        report_path = Path("precise_models_performance_report.json")
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(all_results, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"\n📋 详细性能报告已保存: {report_path}")
    
    return all_results

def main():
    print("🧪 精确架构模型性能测试")
    print("=" * 60)
    print("测试新创建的精确架构模型在各个哭声分类上的识别成功率")
    print("=" * 60)
    
    # 检查 ONNX Runtime
    try:
        import onnxruntime as ort
        print(f"✅ ONNX Runtime 版本: {ort.__version__}")
    except ImportError:
        print("❌ 请安装 ONNX Runtime: pip install onnxruntime")
        return
    
    # 运行测试
    results = test_precise_models()
    
    # 显示最终总结
    if results:
        print(f"\n{'='*60}")
        print("🏆 最终性能总结")
        print("=" * 60)
        
        for model_name, result in results.items():
            print(f"\n📊 {model_name}:")
            print(f"   总体准确率: {result['overall_accuracy']:.3f} ({result['overall_accuracy']*100:.1f}%)")
            print(f"   测试样本数: {result['total_samples']}")
            print(f"   平均推理时间: {result['avg_inference_time']:.3f}s")
            
            # 显示各目录的准确率
            print(f"   各类别准确率:")
            for dir_name, dir_result in result['dir_accuracies'].items():
                print(f"     {dir_name}: {dir_result['accuracy']:.3f} ({dir_result['samples']} 样本)")
    else:
        print("❌ 没有获得任何测试结果")

if __name__ == "__main__":
    main()
