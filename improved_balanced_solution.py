#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的均衡方案：基于方案1，重点提升弱势类别
================================================================================
策略：
1. 保持方案1的稳定架构
2. 针对弱势类别（不适、疲倦）进行数据增强
3. 使用类别特化的损失函数
4. 优化训练策略，保持均衡性
================================================================================
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader, WeightedRandomSampler
import numpy as np
from pathlib import Path
import json
from collections import Counter
from sklearn.model_selection import train_test_split
from tqdm import tqdm
import warnings
import matplotlib.pyplot as plt
warnings.filterwarnings('ignore')

# 导入方案1的稳定模块
from stable_training_v1 import StableFeatureExtractor, StableDeepInfantModel

class ImprovedBalancedDataset(Dataset):
    """改进的均衡数据集 - 重点增强弱势类别"""
    
    def __init__(self, data_dir, feature_extractor, split='train', enhance_weak_classes=True):
        self.data_dir = Path(data_dir)
        self.feature_extractor = feature_extractor
        self.split = split
        self.enhance_weak_classes = enhance_weak_classes
        
        # 类别映射
        self.label_map = {
            'belly_pain': 0,
            'burping': 1, 
            'discomfort': 2,  # 弱势类别
            'hungry': 3,
            'tired': 4        # 弱势类别
        }
        
        self.samples = []
        self._load_data()
        
        if self.enhance_weak_classes and split == 'train':
            self._enhance_weak_classes()
    
    def _load_data(self):
        """加载数据"""
        for label_name, label_idx in self.label_map.items():
            label_dir = self.data_dir / label_name
            if label_dir.exists():
                audio_files = list(label_dir.glob('*.wav'))
                
                # 数据分割
                if len(audio_files) > 0:
                    train_files, test_files = train_test_split(
                        audio_files, test_size=0.2, random_state=42
                    )
                    
                    files_to_use = train_files if self.split == 'train' else test_files
                    
                    for audio_file in files_to_use:
                        self.samples.append((audio_file, label_idx, label_name))
        
        print(f"加载了 {len(self.samples)} 个{self.split}样本")
    
    def _enhance_weak_classes(self):
        """重点增强弱势类别数据"""
        # 统计各类别样本数
        class_counts = Counter([sample[1] for sample in self.samples])
        max_count = max(class_counts.values())
        
        # 弱势类别：不适(2)和疲倦(4)
        weak_classes = [2, 4]  # discomfort, tired
        
        enhanced_samples = []
        for sample in self.samples:
            enhanced_samples.append(sample)
            
            # 对弱势类别进行重点增强
            if sample[1] in weak_classes:
                # 为弱势类别添加更多副本，确保数据平衡
                target_count = max_count
                current_count = class_counts[sample[1]]
                multiplier = target_count // current_count
                
                # 为弱势类别增加更多副本
                for _ in range(min(multiplier - 1, 4)):  # 最多增强4倍
                    enhanced_samples.append(sample)
        
        self.samples = enhanced_samples
        
        # 重新统计
        new_class_counts = Counter([sample[1] for sample in self.samples])
        print(f"弱势类别增强后样本数: {len(self.samples)}")
        print("各类别样本数:")
        for label_name, label_idx in self.label_map.items():
            count = new_class_counts[label_idx]
            print(f"  {label_name}: {count}")
    
    def __len__(self):
        return len(self.samples)
    
    def __getitem__(self, idx):
        audio_file, label, label_name = self.samples[idx]
        
        # 提取特征
        features = self.feature_extractor.extract_features(audio_file)
        
        if features is None:
            # 如果特征提取失败，返回零向量
            features = np.zeros(632)  # 方案1的特征维度
        
        return torch.FloatTensor(features), torch.LongTensor([label])[0]

class WeightedFocalLoss(nn.Module):
    """加权焦点损失 - 重点关注弱势类别和困难样本"""
    
    def __init__(self, alpha=None, gamma=2.0, reduction='mean'):
        super(WeightedFocalLoss, self).__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.reduction = reduction
    
    def forward(self, inputs, targets):
        ce_loss = nn.functional.cross_entropy(inputs, targets, reduction='none')
        pt = torch.exp(-ce_loss)
        
        # 计算焦点权重
        focal_weight = (1 - pt) ** self.gamma
        
        # 计算类别权重
        if self.alpha is not None:
            alpha_t = self.alpha[targets]
            focal_loss = alpha_t * focal_weight * ce_loss
        else:
            focal_loss = focal_weight * ce_loss
        
        if self.reduction == 'mean':
            return focal_loss.mean()
        elif self.reduction == 'sum':
            return focal_loss.sum()
        else:
            return focal_loss

def create_balanced_weighted_sampler(dataset):
    """创建平衡加权采样器 - 重点关注弱势类别"""
    labels = [sample[1] for sample in dataset.samples]
    class_counts = Counter(labels)
    
    # 计算类别权重，对弱势类别给予更高权重
    weights = []
    weak_classes = [2, 4]  # discomfort, tired
    
    for label in labels:
        base_weight = 1.0 / class_counts[label]
        if label in weak_classes:
            # 弱势类别权重额外增强
            weight = base_weight * 2.0
        else:
            weight = base_weight
        weights.append(weight)
    
    return WeightedRandomSampler(weights, len(weights), replacement=True)

def train_improved_balanced_model(model, train_loader, val_loader, num_epochs=100, device='cuda'):
    """训练改进的均衡模型"""
    
    # 计算类别权重 - 重点关注弱势类别
    all_labels = []
    for _, labels in train_loader:
        all_labels.extend(labels.cpu().numpy())
    
    class_counts = Counter(all_labels)
    total_samples = len(all_labels)
    
    # 为弱势类别设置更高权重
    class_weights = []
    weak_classes = [2, 4]  # discomfort, tired
    
    for i in range(5):
        base_weight = total_samples / (5 * class_counts[i])
        if i in weak_classes:
            # 弱势类别权重显著增强
            class_weights.append(base_weight * 3.0)
        else:
            class_weights.append(base_weight)
    
    class_weights = torch.FloatTensor(class_weights).to(device)
    
    # 损失函数 - 使用加权焦点损失
    criterion = WeightedFocalLoss(alpha=class_weights, gamma=2.5)
    
    # 优化器 - 使用较小的学习率确保稳定性
    optimizer = optim.AdamW(model.parameters(), lr=0.0008, weight_decay=0.01)
    
    # 学习率调度器 - 更温和的调度
    scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=num_epochs)
    
    # 训练历史
    history = {'train_loss': [], 'train_acc': [], 'val_loss': [], 'val_acc': []}
    
    best_val_acc = 0.0
    best_model_state = None
    patience_counter = 0
    early_stopping_patience = 25
    
    print(f"开始改进均衡模型训练，设备: {device}")
    print(f"类别权重: {class_weights.cpu().numpy()}")
    
    for epoch in range(num_epochs):
        # 训练阶段
        model.train()
        train_loss = 0.0
        train_correct = 0
        train_total = 0
        
        train_pbar = tqdm(train_loader, desc=f'Epoch {epoch+1}/{num_epochs} [Train]')
        for features, labels in train_pbar:
            features, labels = features.to(device), labels.to(device)
            
            optimizer.zero_grad()
            outputs = model(features)
            loss = criterion(outputs, labels)
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            
            optimizer.step()
            
            train_loss += loss.item()
            _, predicted = torch.max(outputs.data, 1)
            train_total += labels.size(0)
            train_correct += (predicted == labels).sum().item()
            
            train_pbar.set_postfix({
                'Loss': f'{loss.item():.4f}',
                'Acc': f'{100.*train_correct/train_total:.2f}%'
            })
        
        # 验证阶段
        model.eval()
        val_loss = 0.0
        val_correct = 0
        val_total = 0
        
        with torch.no_grad():
            val_pbar = tqdm(val_loader, desc=f'Epoch {epoch+1}/{num_epochs} [Val]')
            for features, labels in val_pbar:
                features, labels = features.to(device), labels.to(device)
                
                outputs = model(features)
                loss = criterion(outputs, labels)
                
                val_loss += loss.item()
                _, predicted = torch.max(outputs.data, 1)
                val_total += labels.size(0)
                val_correct += (predicted == labels).sum().item()
                
                val_pbar.set_postfix({
                    'Loss': f'{loss.item():.4f}',
                    'Acc': f'{100.*val_correct/val_total:.2f}%'
                })
        
        # 计算平均损失和准确率
        avg_train_loss = train_loss / len(train_loader)
        avg_train_acc = 100. * train_correct / train_total
        avg_val_loss = val_loss / len(val_loader)
        avg_val_acc = 100. * val_correct / val_total
        
        # 记录历史
        history['train_loss'].append(avg_train_loss)
        history['train_acc'].append(avg_train_acc)
        history['val_loss'].append(avg_val_loss)
        history['val_acc'].append(avg_val_acc)
        
        # 保存最佳模型
        if avg_val_acc > best_val_acc:
            best_val_acc = avg_val_acc
            best_model_state = model.state_dict().copy()
            patience_counter = 0
        else:
            patience_counter += 1
        
        # 学习率调度
        scheduler.step()
        
        print(f'Epoch {epoch+1}/{num_epochs}:')
        print(f'  Train Loss: {avg_train_loss:.4f}, Train Acc: {avg_train_acc:.2f}%')
        print(f'  Val Loss: {avg_val_loss:.4f}, Val Acc: {avg_val_acc:.2f}%')
        print(f'  Best Val Acc: {best_val_acc:.2f}%')
        print(f'  LR: {optimizer.param_groups[0]["lr"]:.6f}')
        print('-' * 50)
        
        # 早停检查
        if patience_counter >= early_stopping_patience:
            print(f"早停触发！验证准确率在 {early_stopping_patience} 个 epoch 内没有改善。")
            break
    
    # 加载最佳模型
    if best_model_state is not None:
        model.load_state_dict(best_model_state)
    
    return model, history

def evaluate_improved_model(model, test_loader, device):
    """评估改进模型"""
    model.eval()

    all_predictions = []
    all_labels = []
    all_confidences = []

    class_correct = [0] * 5
    class_total = [0] * 5

    label_names = ['belly_pain', 'burping', 'discomfort', 'hungry', 'tired']

    with torch.no_grad():
        for features, labels in tqdm(test_loader, desc='评估中'):
            features, labels = features.to(device), labels.to(device)

            outputs = model(features)
            probabilities = torch.softmax(outputs, dim=1)
            confidences, predicted = torch.max(probabilities, 1)

            all_predictions.extend(predicted.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())
            all_confidences.extend(confidences.cpu().numpy())

            # 统计各类别准确率
            for i in range(labels.size(0)):
                label = labels[i].item()
                class_total[label] += 1
                if predicted[i] == label:
                    class_correct[label] += 1

    # 计算总体准确率
    total_correct = sum(class_correct)
    total_samples = sum(class_total)
    overall_accuracy = total_correct / total_samples if total_samples > 0 else 0

    # 计算各类别准确率
    class_accuracies = {}
    for i in range(5):
        if class_total[i] > 0:
            acc = class_correct[i] / class_total[i]
            class_accuracies[label_names[i]] = acc
        else:
            class_accuracies[label_names[i]] = 0

    return {
        'overall_accuracy': overall_accuracy,
        'class_accuracies': class_accuracies,
        'predictions': all_predictions,
        'labels': all_labels,
        'confidences': all_confidences,
        'total_samples': total_samples
    }

def main():
    """主函数"""
    print("🌟 改进的均衡方案：基于方案1，重点提升弱势类别")
    print("=" * 80)
    print("策略：")
    print("1. 保持方案1的稳定架构")
    print("2. 针对弱势类别（不适、疲倦）进行数据增强")
    print("3. 使用类别特化的损失函数")
    print("4. 优化训练策略，保持均衡性")
    print("=" * 80)

    # 设备配置
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")

    # 数据目录
    data_dir = Path("Data")
    output_dir = Path("improved_balanced_results")
    output_dir.mkdir(exist_ok=True)

    # 创建特征提取器
    print("\n🔧 创建稳定特征提取器...")
    feature_extractor = StableFeatureExtractor()

    # 创建改进的均衡数据集
    print("\n📊 创建改进的均衡数据集...")
    train_dataset = ImprovedBalancedDataset(
        data_dir, feature_extractor, split='train', enhance_weak_classes=True
    )
    val_dataset = ImprovedBalancedDataset(
        data_dir, feature_extractor, split='test', enhance_weak_classes=False
    )

    if len(train_dataset) == 0:
        print("❌ 训练数据集为空")
        return

    # 获取特征维度
    sample_features, _ = train_dataset[0]
    input_dim = sample_features.shape[0]
    print(f"特征维度: {input_dim}")

    # 创建数据加载器
    train_sampler = create_balanced_weighted_sampler(train_dataset)
    train_loader = DataLoader(train_dataset, batch_size=32, sampler=train_sampler)
    val_loader = DataLoader(val_dataset, batch_size=32, shuffle=False)

    # 创建模型 - 使用方案1的稳定架构
    print(f"\n🏗️ 创建改进的均衡模型...")
    model = StableDeepInfantModel(input_dim=input_dim, num_classes=5)
    model = model.to(device)

    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")

    # 训练模型
    print("\n🎯 开始改进均衡模型训练...")
    trained_model, history = train_improved_balanced_model(model, train_loader, val_loader, num_epochs=80, device=device)

    # 保存模型
    model_path = output_dir / "improved_balanced_model.pth"
    torch.save(trained_model.state_dict(), model_path)
    print(f"模型已保存: {model_path}")

    # 保存完整模型（包含架构）
    full_model_path = output_dir / "improved_balanced_full_model.pth"
    torch.save(trained_model, full_model_path)
    print(f"完整模型已保存: {full_model_path}")

    # 评估模型
    print("\n📊 评估改进均衡模型...")
    results = evaluate_improved_model(trained_model, val_loader, device)

    # 显示结果
    print(f"\n🎯 改进均衡方案性能:")
    print(f"   总体准确率: {results['overall_accuracy']:.3f} ({results['overall_accuracy']*100:.1f}%)")
    print(f"   测试样本数: {results['total_samples']}")

    print(f"\n   各类别准确率:")
    class_names_cn = {
        'belly_pain': '腹痛',
        'burping': '打嗝',
        'discomfort': '不适',
        'hungry': '饥饿',
        'tired': '疲倦'
    }

    for class_name, accuracy in results['class_accuracies'].items():
        cn_name = class_names_cn[class_name]
        print(f"     {cn_name}: {accuracy:.3f} ({accuracy*100:.1f}%)")

    # 与现有方案对比
    print(f"\n📈 与现有方案对比:")
    print(f"   方案1准确率: 74.6%")
    print(f"   方案2准确率: 90.2%")
    print(f"   改进均衡方案准确率: {results['overall_accuracy']*100:.1f}%")

    improvement_from_v1 = (results['overall_accuracy'] * 100) - 74.6
    improvement_from_v2 = (results['overall_accuracy'] * 100) - 90.2
    print(f"   相比方案1提升: {improvement_from_v1:+.1f} 个百分点")
    print(f"   相比方案2提升: {improvement_from_v2:+.1f} 个百分点")

    # 分析均衡性
    category_accs = [acc * 100 for acc in results['class_accuracies'].values()]
    std_dev = np.std(category_accs)
    print(f"\n🎯 均衡性分析:")
    print(f"   类别间标准差: {std_dev:.1f}% (越小越均衡)")
    print(f"   最高类别准确率: {max(category_accs):.1f}%")
    print(f"   最低类别准确率: {min(category_accs):.1f}%")

    # 重点关注弱势类别改进
    weak_classes = ['discomfort', 'tired']
    print(f"\n🎯 弱势类别改进效果:")
    for class_name in weak_classes:
        cn_name = class_names_cn[class_name]
        acc = results['class_accuracies'][class_name]
        print(f"   {cn_name}: {acc:.3f} ({acc*100:.1f}%)")

    # 保存详细结果
    detailed_results = {
        'method': 'Improved Balanced Solution',
        'overall_accuracy': results['overall_accuracy'],
        'total_samples': results['total_samples'],
        'class_accuracies': results['class_accuracies'],
        'improvement_over_v1': improvement_from_v1,
        'improvement_over_v2': improvement_from_v2,
        'category_balance_std': std_dev,
        'training_history': history
    }

    results_path = output_dir / "improved_balanced_results.json"
    with open(results_path, 'w', encoding='utf-8') as f:
        json.dump(detailed_results, f, indent=2, ensure_ascii=False)

    print(f"\n📋 详细结果已保存: {results_path}")
    print(f"\n✅ 改进均衡方案训练完成！")

if __name__ == "__main__":
    main()
