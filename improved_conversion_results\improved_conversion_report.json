[{"model_path": "Models/DeepInfant_VGGish.mlmodel", "analysis": {"model_path": "Models/DeepInfant_VGGish.mlmodel", "model_type": "pipelineClassifier", "specification_version": 4, "inputs": [{"name": "audioSamples", "type": "multiArrayType", "shape": [15600], "data_type": "65568"}], "outputs": [{"name": "classLabelProbs", "type": "dictionaryType"}, {"name": "classLabel", "type": "stringType"}], "layers": [{"name": "conv1", "type": "convolution", "inputs": ["preprocessedAudio"], "outputs": ["conv1_output"], "output_channels": 64, "kernel_size": [3, 3], "stride": [1, 1], "padding_type": "valid", "has_bias": true}, {"name": "conv1__activation__", "type": "activation", "inputs": ["conv1_output"], "outputs": ["conv1__activation___output"], "activation_type": "ReLU"}, {"name": "pool1", "type": "0", "inputs": ["conv1__activation___output"], "outputs": ["pool1_output"], "kernel_size": [2, 2], "stride": [2, 2]}, {"name": "conv2", "type": "convolution", "inputs": ["pool1_output"], "outputs": ["conv2_output"], "output_channels": 128, "kernel_size": [3, 3], "stride": [1, 1], "padding_type": "valid", "has_bias": true}, {"name": "conv2__activation__", "type": "activation", "inputs": ["conv2_output"], "outputs": ["conv2__activation___output"], "activation_type": "ReLU"}, {"name": "pool2", "type": "0", "inputs": ["conv2__activation___output"], "outputs": ["pool2_output"], "kernel_size": [2, 2], "stride": [2, 2]}, {"name": "conv3_1", "type": "convolution", "inputs": ["pool2_output"], "outputs": ["conv3_1_output"], "output_channels": 256, "kernel_size": [3, 3], "stride": [1, 1], "padding_type": "valid", "has_bias": true}, {"name": "conv3_1__activation__", "type": "activation", "inputs": ["conv3_1_output"], "outputs": ["conv3_1__activation___output"], "activation_type": "ReLU"}, {"name": "conv3_2", "type": "convolution", "inputs": ["conv3_1__activation___output"], "outputs": ["conv3_2_output"], "output_channels": 256, "kernel_size": [3, 3], "stride": [1, 1], "padding_type": "valid", "has_bias": true}, {"name": "conv3_2__activation__", "type": "activation", "inputs": ["conv3_2_output"], "outputs": ["conv3_2__activation___output"], "activation_type": "ReLU"}, {"name": "pool3", "type": "0", "inputs": ["conv3_2__activation___output"], "outputs": ["pool3_output"], "kernel_size": [2, 2], "stride": [2, 2]}, {"name": "conv4_1", "type": "convolution", "inputs": ["pool3_output"], "outputs": ["conv4_1_output"], "output_channels": 512, "kernel_size": [3, 3], "stride": [1, 1], "padding_type": "valid", "has_bias": true}, {"name": "conv4_1__activation__", "type": "activation", "inputs": ["conv4_1_output"], "outputs": ["conv4_1__activation___output"], "activation_type": "ReLU"}, {"name": "conv4_2", "type": "convolution", "inputs": ["conv4_1__activation___output"], "outputs": ["conv4_2_output"], "output_channels": 512, "kernel_size": [3, 3], "stride": [1, 1], "padding_type": "valid", "has_bias": true}, {"name": "conv4_2__activation__", "type": "activation", "inputs": ["conv4_2_output"], "outputs": ["conv4_2__activation___output"], "activation_type": "ReLU"}, {"name": "pool4", "type": "0", "inputs": ["conv4_2__activation___output"], "outputs": ["pool4_output"], "kernel_size": [2, 2], "stride": [2, 2]}, {"name": "flatten", "type": "flatten", "inputs": ["pool4_output"], "outputs": ["features"]}], "class_labels": null, "status": "success", "layer_count": 17, "num_classes": null}, "onnx_conversion": {"status": "error", "error": "Unable to determine the type of the model, i.e. the source framework. Please provide the value of argument \"source\", from one of [\"tensorflow\", \"pytorch\", \"milinternal\"]. Note that model conversion requires the source package that generates the model. Please make sure you have the appropriate version of source package installed. E.g., if you're converting model originally trained with TensorFlow 1.14, make sure you have `tensorflow==1.14` installed."}, "pytorch_model": {"status": "success", "num_classes": 9, "saved_path": "improved_conversion_results\\DeepInfant_VGGish_analyzed.pth"}}, {"model_path": "Models/DeepInfant_AFP.mlmodel", "analysis": {"model_path": "Models/DeepInfant_AFP.mlmodel", "model_type": "pipelineClassifier", "specification_version": 6, "inputs": [{"name": "audioSamples", "type": "multiArrayType", "shape": [80000], "data_type": "65568"}], "outputs": [{"name": "classLabelProbs", "type": "dictionaryType"}, {"name": "classLabel", "type": "stringType"}], "layers": [], "class_labels": null, "status": "success", "layer_count": 0, "num_classes": null}, "onnx_conversion": {"status": "error", "error": "Unable to determine the type of the model, i.e. the source framework. Please provide the value of argument \"source\", from one of [\"tensorflow\", \"pytorch\", \"milinternal\"]. Note that model conversion requires the source package that generates the model. Please make sure you have the appropriate version of source package installed. E.g., if you're converting model originally trained with TensorFlow 1.14, make sure you have `tensorflow==1.14` installed."}, "pytorch_model": {"status": "success", "num_classes": 9, "saved_path": "improved_conversion_results\\DeepInfant_AFP_analyzed.pth"}}, {"model_path": "iOS_iPadOS/deepinfant/model/DeepInfant_VGGish.mlmodel", "analysis": {"model_path": "iOS_iPadOS/deepinfant/model/DeepInfant_VGGish.mlmodel", "model_type": "pipelineClassifier", "specification_version": 4, "inputs": [{"name": "audioSamples", "type": "multiArrayType", "shape": [15600], "data_type": "65568"}], "outputs": [{"name": "classLabelProbs", "type": "dictionaryType"}, {"name": "classLabel", "type": "stringType"}], "layers": [{"name": "conv1", "type": "convolution", "inputs": ["preprocessedAudio"], "outputs": ["conv1_output"], "output_channels": 64, "kernel_size": [3, 3], "stride": [1, 1], "padding_type": "valid", "has_bias": true}, {"name": "conv1__activation__", "type": "activation", "inputs": ["conv1_output"], "outputs": ["conv1__activation___output"], "activation_type": "ReLU"}, {"name": "pool1", "type": "0", "inputs": ["conv1__activation___output"], "outputs": ["pool1_output"], "kernel_size": [2, 2], "stride": [2, 2]}, {"name": "conv2", "type": "convolution", "inputs": ["pool1_output"], "outputs": ["conv2_output"], "output_channels": 128, "kernel_size": [3, 3], "stride": [1, 1], "padding_type": "valid", "has_bias": true}, {"name": "conv2__activation__", "type": "activation", "inputs": ["conv2_output"], "outputs": ["conv2__activation___output"], "activation_type": "ReLU"}, {"name": "pool2", "type": "0", "inputs": ["conv2__activation___output"], "outputs": ["pool2_output"], "kernel_size": [2, 2], "stride": [2, 2]}, {"name": "conv3_1", "type": "convolution", "inputs": ["pool2_output"], "outputs": ["conv3_1_output"], "output_channels": 256, "kernel_size": [3, 3], "stride": [1, 1], "padding_type": "valid", "has_bias": true}, {"name": "conv3_1__activation__", "type": "activation", "inputs": ["conv3_1_output"], "outputs": ["conv3_1__activation___output"], "activation_type": "ReLU"}, {"name": "conv3_2", "type": "convolution", "inputs": ["conv3_1__activation___output"], "outputs": ["conv3_2_output"], "output_channels": 256, "kernel_size": [3, 3], "stride": [1, 1], "padding_type": "valid", "has_bias": true}, {"name": "conv3_2__activation__", "type": "activation", "inputs": ["conv3_2_output"], "outputs": ["conv3_2__activation___output"], "activation_type": "ReLU"}, {"name": "pool3", "type": "0", "inputs": ["conv3_2__activation___output"], "outputs": ["pool3_output"], "kernel_size": [2, 2], "stride": [2, 2]}, {"name": "conv4_1", "type": "convolution", "inputs": ["pool3_output"], "outputs": ["conv4_1_output"], "output_channels": 512, "kernel_size": [3, 3], "stride": [1, 1], "padding_type": "valid", "has_bias": true}, {"name": "conv4_1__activation__", "type": "activation", "inputs": ["conv4_1_output"], "outputs": ["conv4_1__activation___output"], "activation_type": "ReLU"}, {"name": "conv4_2", "type": "convolution", "inputs": ["conv4_1__activation___output"], "outputs": ["conv4_2_output"], "output_channels": 512, "kernel_size": [3, 3], "stride": [1, 1], "padding_type": "valid", "has_bias": true}, {"name": "conv4_2__activation__", "type": "activation", "inputs": ["conv4_2_output"], "outputs": ["conv4_2__activation___output"], "activation_type": "ReLU"}, {"name": "pool4", "type": "0", "inputs": ["conv4_2__activation___output"], "outputs": ["pool4_output"], "kernel_size": [2, 2], "stride": [2, 2]}, {"name": "flatten", "type": "flatten", "inputs": ["pool4_output"], "outputs": ["features"]}], "class_labels": null, "status": "success", "layer_count": 17, "num_classes": null}, "onnx_conversion": {"status": "error", "error": "Unable to determine the type of the model, i.e. the source framework. Please provide the value of argument \"source\", from one of [\"tensorflow\", \"pytorch\", \"milinternal\"]. Note that model conversion requires the source package that generates the model. Please make sure you have the appropriate version of source package installed. E.g., if you're converting model originally trained with TensorFlow 1.14, make sure you have `tensorflow==1.14` installed."}, "pytorch_model": {"status": "success", "num_classes": 9, "saved_path": "improved_conversion_results\\DeepInfant_VGGish_analyzed.pth"}}, {"model_path": "iOS_iPadOS/deepinfant/model/DeepInfant_AFP.mlmodel", "analysis": {"model_path": "iOS_iPadOS/deepinfant/model/DeepInfant_AFP.mlmodel", "model_type": "pipelineClassifier", "specification_version": 6, "inputs": [{"name": "audioSamples", "type": "multiArrayType", "shape": [80000], "data_type": "65568"}], "outputs": [{"name": "classLabelProbs", "type": "dictionaryType"}, {"name": "classLabel", "type": "stringType"}], "layers": [], "class_labels": null, "status": "success", "layer_count": 0, "num_classes": null}, "onnx_conversion": {"status": "error", "error": "Unable to determine the type of the model, i.e. the source framework. Please provide the value of argument \"source\", from one of [\"tensorflow\", \"pytorch\", \"milinternal\"]. Note that model conversion requires the source package that generates the model. Please make sure you have the appropriate version of source package installed. E.g., if you're converting model originally trained with TensorFlow 1.14, make sure you have `tensorflow==1.14` installed."}, "pytorch_model": {"status": "success", "num_classes": 9, "saved_path": "improved_conversion_results\\DeepInfant_AFP_analyzed.pth"}}]