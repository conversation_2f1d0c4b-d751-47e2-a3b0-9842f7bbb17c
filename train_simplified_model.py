#!/usr/bin/env python3
"""
简化版本的精确架构模型训练

这个脚本不依赖 TensorFlow Hub，使用高级音频特征提取作为 backbone，
实现对精确架构模型的微调训练。

作者: DeepInfant Team
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader, WeightedRandomSampler
import torch.nn.functional as F
import librosa
import numpy as np
from pathlib import Path
import json
from collections import defaultdict, Counter
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, confusion_matrix
import matplotlib.pyplot as plt
import seaborn as sns
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

class AdvancedAudioFeatureExtractor:
    """高级音频特征提取器 (替代 YAMNet)"""
    
    def __init__(self):
        self.sr = 16000
        self.n_fft = 1024
        self.hop_length = 512
        self.n_mels = 128
        
    def extract_features(self, audio_path, target_length=16000*3):
        """提取综合音频特征"""
        try:
            # 加载音频
            audio, sr = librosa.load(audio_path, sr=self.sr)
            
            # 调整长度
            if len(audio) < target_length:
                audio = np.pad(audio, (0, target_length - len(audio)), mode='constant')
            else:
                audio = audio[:target_length]
            
            # 1. Mel 频谱特征
            mel_spec = librosa.feature.melspectrogram(
                y=audio, sr=sr, n_fft=self.n_fft, 
                hop_length=self.hop_length, n_mels=self.n_mels
            )
            mel_spec_db = librosa.power_to_db(mel_spec, ref=np.max)
            
            # 2. MFCC 特征
            mfcc = librosa.feature.mfcc(
                y=audio, sr=sr, n_mfcc=13, n_fft=self.n_fft, hop_length=self.hop_length
            )
            
            # 3. 色度特征
            chroma = librosa.feature.chroma_stft(
                y=audio, sr=sr, n_fft=self.n_fft, hop_length=self.hop_length
            )
            
            # 4. 谱质心
            spectral_centroids = librosa.feature.spectral_centroid(
                y=audio, sr=sr, hop_length=self.hop_length
            )
            
            # 5. 谱带宽
            spectral_bandwidth = librosa.feature.spectral_bandwidth(
                y=audio, sr=sr, hop_length=self.hop_length
            )
            
            # 6. 谱滚降
            spectral_rolloff = librosa.feature.spectral_rolloff(
                y=audio, sr=sr, hop_length=self.hop_length
            )
            
            # 7. 零交叉率
            zcr = librosa.feature.zero_crossing_rate(audio, hop_length=self.hop_length)
            
            # 8. RMS 能量
            rms = librosa.feature.rms(y=audio, hop_length=self.hop_length)
            
            # 统计特征提取
            features = []
            
            # 对每种特征计算统计量
            for feature_matrix in [mel_spec_db, mfcc, chroma, spectral_centroids, 
                                 spectral_bandwidth, spectral_rolloff, zcr, rms]:
                features.extend([
                    np.mean(feature_matrix, axis=1),
                    np.std(feature_matrix, axis=1),
                    np.max(feature_matrix, axis=1),
                    np.min(feature_matrix, axis=1),
                    np.median(feature_matrix, axis=1)
                ])
            
            # 展平所有特征
            features = np.concatenate([f.flatten() for f in features])
            
            # 添加一些全局统计特征
            global_features = [
                np.mean(audio),
                np.std(audio),
                np.max(audio),
                np.min(audio),
                len(audio),
                np.sum(audio**2),  # 总能量
            ]
            
            features = np.concatenate([features, global_features])
            
            return features.astype(np.float32)
            
        except Exception as e:
            print(f"特征提取失败 {audio_path}: {e}")
            # 返回零特征作为备选
            return np.zeros(1000, dtype=np.float32)  # 估计的特征维度

class InfantCryDataset(Dataset):
    """婴儿哭声数据集"""
    
    def __init__(self, data_dir, feature_extractor, split='train', test_size=0.2, random_state=42):
        self.feature_extractor = feature_extractor
        self.label_map = {
            'bp': 0, 'bu': 1, 'dc': 2, 'hu': 3, 'ti': 4
        }
        self.label_names = {
            0: 'belly_pain', 1: 'burping', 2: 'discomfort', 3: 'hungry', 4: 'tired'
        }
        
        # 收集数据
        self.data = self._collect_data(data_dir)
        
        # 划分训练/测试集
        if len(self.data) > 0:
            train_data, test_data = train_test_split(
                self.data, test_size=test_size, random_state=random_state, 
                stratify=[item[1] for item in self.data]
            )
            
            if split == 'train':
                self.data = train_data
            else:
                self.data = test_data
        
        print(f"{split} 集大小: {len(self.data)}")
        if len(self.data) > 0:
            label_counts = Counter([item[1] for item in self.data])
            print(f"标签分布: {dict(label_counts)}")
    
    def _collect_data(self, data_dir):
        """收集数据文件"""
        data = []
        data_dir = Path(data_dir)
        
        # 目录到标签的映射
        dir_to_label = {
            'belly_pain': 'bp',
            'burping': 'bu', 
            'discomfort': 'dc',
            'hungry': 'hu',
            'tired': 'ti'
        }
        
        for dir_name, label in dir_to_label.items():
            dir_path = data_dir / dir_name
            if dir_path.exists():
                audio_files = list(dir_path.glob('*.wav'))
                for audio_file in audio_files:
                    data.append((str(audio_file), self.label_map[label]))
                print(f"从 {dir_name} 收集了 {len(audio_files)} 个文件")
        
        return data
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        audio_path, label = self.data[idx]
        
        # 提取特征
        features = self.feature_extractor.extract_features(audio_path)
        
        return torch.FloatTensor(features), torch.LongTensor([label])[0]

class ImprovedDeepInfantModel(nn.Module):
    """改进的 DeepInfant 模型"""
    
    def __init__(self, input_dim, num_classes=5, dropout=0.5):
        super(ImprovedDeepInfantModel, self).__init__()
        
        # 自适应隐藏层维度
        hidden_dims = [
            min(input_dim, 512),
            min(input_dim // 2, 256),
            min(input_dim // 4, 128),
            64
        ]
        
        # 构建特征提取器
        layers = []
        prev_dim = input_dim
        
        for i, hidden_dim in enumerate(hidden_dims):
            layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.BatchNorm1d(hidden_dim),
                nn.ReLU(inplace=True),
                nn.Dropout(dropout if i < len(hidden_dims) - 1 else dropout/2)
            ])
            prev_dim = hidden_dim
        
        self.feature_extractor = nn.Sequential(*layers)
        
        # 分类器
        self.classifier = nn.Sequential(
            nn.Linear(prev_dim, num_classes)
        )
        
        # 权重初始化
        self._initialize_weights()
    
    def _initialize_weights(self):
        """初始化权重"""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.BatchNorm1d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
    
    def forward(self, x):
        features = self.feature_extractor(x)
        output = self.classifier(features)
        return output

def create_balanced_sampler(dataset):
    """创建平衡采样器"""
    labels = [dataset[i][1].item() for i in range(len(dataset))]
    label_counts = Counter(labels)
    
    # 计算权重
    total_samples = len(labels)
    num_classes = len(label_counts)
    
    weights = []
    for label in labels:
        weight = total_samples / (num_classes * label_counts[label])
        weights.append(weight)
    
    return WeightedRandomSampler(weights, total_samples, replacement=True)

def train_model(model, train_loader, val_loader, num_epochs=100, device='cpu'):
    """训练模型"""
    
    # 损失函数和优化器
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.AdamW(model.parameters(), lr=0.001, weight_decay=0.01)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', patience=10, factor=0.5)
    
    # 训练历史
    history = {
        'train_loss': [], 'train_acc': [],
        'val_loss': [], 'val_acc': []
    }
    
    best_val_acc = 0.0
    best_model_state = None
    patience_counter = 0
    early_stopping_patience = 20
    
    print(f"开始训练，设备: {device}")
    
    for epoch in range(num_epochs):
        # 训练阶段
        model.train()
        train_loss = 0.0
        train_correct = 0
        train_total = 0
        
        train_pbar = tqdm(train_loader, desc=f'Epoch {epoch+1}/{num_epochs} [Train]')
        for features, labels in train_pbar:
            features, labels = features.to(device), labels.to(device)
            
            optimizer.zero_grad()
            outputs = model(features)
            loss = criterion(outputs, labels)
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            
            optimizer.step()
            
            train_loss += loss.item()
            _, predicted = torch.max(outputs.data, 1)
            train_total += labels.size(0)
            train_correct += (predicted == labels).sum().item()
            
            # 更新进度条
            train_pbar.set_postfix({
                'Loss': f'{loss.item():.4f}',
                'Acc': f'{100.*train_correct/train_total:.2f}%'
            })
        
        # 验证阶段
        model.eval()
        val_loss = 0.0
        val_correct = 0
        val_total = 0
        
        with torch.no_grad():
            val_pbar = tqdm(val_loader, desc=f'Epoch {epoch+1}/{num_epochs} [Val]')
            for features, labels in val_pbar:
                features, labels = features.to(device), labels.to(device)
                outputs = model(features)
                loss = criterion(outputs, labels)
                
                val_loss += loss.item()
                _, predicted = torch.max(outputs.data, 1)
                val_total += labels.size(0)
                val_correct += (predicted == labels).sum().item()
                
                val_pbar.set_postfix({
                    'Loss': f'{loss.item():.4f}',
                    'Acc': f'{100.*val_correct/val_total:.2f}%'
                })
        
        # 计算平均指标
        avg_train_loss = train_loss / len(train_loader)
        avg_train_acc = 100. * train_correct / train_total
        avg_val_loss = val_loss / len(val_loader)
        avg_val_acc = 100. * val_correct / val_total
        
        # 记录历史
        history['train_loss'].append(avg_train_loss)
        history['train_acc'].append(avg_train_acc)
        history['val_loss'].append(avg_val_loss)
        history['val_acc'].append(avg_val_acc)
        
        # 学习率调度
        scheduler.step(avg_val_loss)
        
        # 保存最佳模型和早停
        if avg_val_acc > best_val_acc:
            best_val_acc = avg_val_acc
            best_model_state = model.state_dict().copy()
            patience_counter = 0
        else:
            patience_counter += 1
        
        print(f'Epoch {epoch+1}/{num_epochs}:')
        print(f'  Train Loss: {avg_train_loss:.4f}, Train Acc: {avg_train_acc:.2f}%')
        print(f'  Val Loss: {avg_val_loss:.4f}, Val Acc: {avg_val_acc:.2f}%')
        print(f'  Best Val Acc: {best_val_acc:.2f}%')
        print(f'  LR: {optimizer.param_groups[0]["lr"]:.6f}')
        print('-' * 50)
        
        # 早停检查
        if patience_counter >= early_stopping_patience:
            print(f"早停触发！验证准确率在 {early_stopping_patience} 个 epoch 内没有改善。")
            break
    
    # 加载最佳模型
    if best_model_state is not None:
        model.load_state_dict(best_model_state)
    
    return model, history

def main():
    print("🚀 简化版精确架构模型训练")
    print("=" * 60)
    
    # 设备选择
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 创建输出目录
    output_dir = Path("simplified_training_results")
    output_dir.mkdir(exist_ok=True)
    
    # 初始化特征提取器
    print("\n📥 初始化高级音频特征提取器...")
    feature_extractor = AdvancedAudioFeatureExtractor()
    
    # 创建数据集
    print("\n📊 创建数据集...")
    train_dataset = InfantCryDataset('Data', feature_extractor, split='train')
    val_dataset = InfantCryDataset('Data', feature_extractor, split='test')
    
    if len(train_dataset) == 0 or len(val_dataset) == 0:
        print("❌ 数据集为空，请检查数据目录")
        return
    
    # 获取特征维度
    sample_features, _ = train_dataset[0]
    input_dim = sample_features.shape[0]
    print(f"特征维度: {input_dim}")
    
    # 创建数据加载器
    train_sampler = create_balanced_sampler(train_dataset)
    train_loader = DataLoader(train_dataset, batch_size=16, sampler=train_sampler)
    val_loader = DataLoader(val_dataset, batch_size=16, shuffle=False)
    
    # 创建模型
    print(f"\n🏗️ 创建模型...")
    model = ImprovedDeepInfantModel(input_dim=input_dim, num_classes=5)
    model = model.to(device)
    
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 训练模型
    print("\n🎯 开始训练...")
    trained_model, history = train_model(model, train_loader, val_loader, num_epochs=100, device=device)
    
    # 保存模型
    model_path = output_dir / "improved_deepinfant_model.pth"
    torch.save(trained_model.state_dict(), model_path)
    print(f"✅ 模型已保存: {model_path}")
    
    # 保存完整模型
    full_model_path = output_dir / "improved_deepinfant_full_model.pth"
    torch.save(trained_model, full_model_path)
    print(f"✅ 完整模型已保存: {full_model_path}")
    
    # 保存训练结果
    results = {
        'training_history': history,
        'model_config': {
            'input_dim': input_dim,
            'num_classes': 5,
            'feature_extractor': 'Advanced Audio Features',
            'device': str(device)
        }
    }
    
    results_path = output_dir / "training_results.json"
    with open(results_path, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"✅ 训练结果已保存: {results_path}")
    print(f"📁 所有输出文件保存在: {output_dir}")
    
    # 显示最终结果
    final_train_acc = history['train_acc'][-1]
    final_val_acc = history['val_acc'][-1]
    best_val_acc = max(history['val_acc'])
    
    print(f"\n🎯 训练完成！")
    print(f"最终训练准确率: {final_train_acc:.2f}%")
    print(f"最终验证准确率: {final_val_acc:.2f}%")
    print(f"最佳验证准确率: {best_val_acc:.2f}%")

if __name__ == "__main__":
    main()
