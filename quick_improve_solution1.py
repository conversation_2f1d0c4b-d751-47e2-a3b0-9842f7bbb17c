#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速改进方案1 - 针对弱势类别的轻量级优化
================================================================================
基于现有方案1，快速实现针对不适和疲倦类别的改进
不需要重新训练，直接在现有模型基础上优化
================================================================================
"""

import torch
import torch.nn as nn
import numpy as np
from pathlib import Path
import json
from collections import Counter
import time
from tqdm import tqdm

# 导入现有的稳定训练模块
try:
    from stable_training_v1 import StableFeatureExtractor, StableDeepInfantModel, StableInfantCryDataset
except ImportError:
    print("❌ 请确保 stable_training_v1.py 文件存在")
    exit(1)

class WeakClassBooster:
    """弱势类别增强器 - 针对不适和疲倦类别的后处理优化"""
    
    def __init__(self, weak_classes=[2, 4], boost_factor=1.5):
        """
        Args:
            weak_classes: 弱势类别索引 [2: discomfort, 4: tired]
            boost_factor: 增强因子
        """
        self.weak_classes = weak_classes
        self.boost_factor = boost_factor
        
        # 基于分析的类别特征阈值
        self.class_thresholds = {
            0: 0.5,   # belly_pain
            1: 0.6,   # burping  
            2: 0.3,   # discomfort (降低阈值)
            3: 0.7,   # hungry
            4: 0.3    # tired (降低阈值)
        }
    
    def boost_predictions(self, logits, apply_boost=True):
        """增强弱势类别的预测概率"""
        if not apply_boost:
            return logits
        
        # 复制logits避免修改原始数据
        boosted_logits = logits.clone()
        
        # 对弱势类别应用增强
        for weak_class in self.weak_classes:
            boosted_logits[:, weak_class] *= self.boost_factor
        
        return boosted_logits
    
    def adaptive_threshold(self, probabilities, predicted_classes):
        """自适应阈值调整"""
        adjusted_predictions = predicted_classes.clone()
        
        for i, (prob, pred) in enumerate(zip(probabilities, predicted_classes)):
            # 如果预测的是强势类别，但弱势类别概率也较高，则重新考虑
            if pred.item() not in self.weak_classes:
                for weak_class in self.weak_classes:
                    weak_prob = prob[weak_class].item()
                    pred_prob = prob[pred].item()
                    
                    # 如果弱势类别概率超过其特定阈值，且与预测类别概率差距不大
                    if (weak_prob > self.class_thresholds[weak_class] and 
                        pred_prob - weak_prob < 0.3):
                        adjusted_predictions[i] = weak_class
                        break
        
        return adjusted_predictions

class ImprovedStableModel(nn.Module):
    """改进的稳定模型 - 在原有基础上添加弱势类别增强"""
    
    def __init__(self, original_model, weak_class_booster):
        super(ImprovedStableModel, self).__init__()
        self.original_model = original_model
        self.booster = weak_class_booster
        self.training = True  # 控制是否应用增强
    
    def forward(self, x):
        # 获取原始模型输出
        logits = self.original_model(x)
        
        # 在推理时应用弱势类别增强
        if not self.training:
            logits = self.booster.boost_predictions(logits, apply_boost=True)
        
        return logits
    
    def predict_with_boost(self, x):
        """带增强的预测"""
        self.eval()
        with torch.no_grad():
            logits = self.forward(x)
            probabilities = torch.softmax(logits, dim=1)
            _, predicted = torch.max(probabilities, 1)
            
            # 应用自适应阈值
            adjusted_predictions = self.booster.adaptive_threshold(probabilities, predicted)
            
            return adjusted_predictions, probabilities

def load_original_model(model_path, data_dir="Data"):
    """加载原始的稳定模型，自动检测特征维度"""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

    # 先创建特征提取器来确定维度
    feature_extractor = StableFeatureExtractor()

    # 找一个样本文件来确定特征维度
    data_path = Path(data_dir)
    sample_file = None
    for subdir in ['belly_pain', 'burping', 'discomfort', 'hungry', 'tired']:
        subdir_path = data_path / subdir
        if subdir_path.exists():
            wav_files = list(subdir_path.glob('*.wav'))
            if wav_files:
                sample_file = wav_files[0]
                break

    if sample_file is None:
        print("❌ 未找到样本音频文件来确定特征维度")
        return None

    # 提取特征确定维度
    sample_features = feature_extractor.extract_features(str(sample_file))
    input_dim = len(sample_features)
    print(f"检测到特征维度: {input_dim}")

    # 创建原始模型
    original_model = StableDeepInfantModel(input_dim=input_dim, num_classes=5)

    # 加载权重
    if Path(model_path).exists():
        state_dict = torch.load(model_path, map_location=device)
        original_model.load_state_dict(state_dict)
        print(f"✅ 成功加载原始模型: {model_path}")
    else:
        print(f"❌ 未找到模型文件: {model_path}")
        return None

    return original_model.to(device)

def evaluate_improved_model(model, test_loader, device='cuda'):
    """评估改进后的模型"""
    model.eval()
    
    all_predictions = []
    all_labels = []
    all_confidences = []
    inference_times = []
    
    class_correct = [0] * 5
    class_total = [0] * 5
    
    label_names = ['belly_pain', 'burping', 'discomfort', 'hungry', 'tired']
    
    with torch.no_grad():
        for features, labels in tqdm(test_loader, desc="评估改进模型"):
            features, labels = features.to(device), labels.to(device)
            
            # 测量推理时间
            start_time = time.time()
            predictions, probabilities = model.predict_with_boost(features)
            inference_time = time.time() - start_time
            inference_times.append(inference_time / features.size(0))
            
            # 获取置信度
            confidences = torch.max(probabilities, 1)[0]
            
            # 收集结果
            all_predictions.extend(predictions.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())
            all_confidences.extend(confidences.cpu().numpy())
            
            # 统计各类别准确率
            for i in range(labels.size(0)):
                label = labels[i].item()
                class_total[label] += 1
                if predictions[i] == labels[i]:
                    class_correct[label] += 1
    
    # 计算结果
    total_correct = sum(class_correct)
    total_samples = sum(class_total)
    overall_accuracy = total_correct / total_samples if total_samples > 0 else 0
    
    class_accuracies = {}
    for i in range(5):
        if class_total[i] > 0:
            acc = class_correct[i] / class_total[i]
            class_accuracies[label_names[i]] = acc
        else:
            class_accuracies[label_names[i]] = 0
    
    return {
        'overall_accuracy': overall_accuracy,
        'class_accuracies': class_accuracies,
        'predictions': all_predictions,
        'labels': all_labels,
        'confidences': all_confidences,
        'avg_inference_time': np.mean(inference_times),
        'total_samples': total_samples,
        'class_correct': class_correct,
        'class_total': class_total
    }

def compare_results(original_results, improved_results):
    """对比原始和改进结果"""
    print("\n📊 快速改进效果对比")
    print("=" * 80)
    
    # 总体准确率对比
    orig_acc = original_results.get('overall_accuracy', 0)
    impr_acc = improved_results['overall_accuracy']
    improvement = impr_acc - orig_acc
    
    print(f"总体准确率:")
    print(f"  原方案1: {orig_acc:.1%}")
    print(f"  快速改进: {impr_acc:.1%}")
    print(f"  提升: {improvement:+.1%}")
    
    # 各类别对比
    print(f"\n各类别准确率对比:")
    class_names_cn = ['腹痛', '打嗝', '不适', '饥饿', '疲倦']
    class_keys = ['belly_pain', 'burping', 'discomfort', 'hungry', 'tired']
    
    for i, (cn_name, key) in enumerate(zip(class_names_cn, class_keys)):
        orig_class_acc = original_results.get('class_accuracies', {}).get(key, 0)
        impr_class_acc = improved_results['class_accuracies'][key]
        class_improvement = impr_class_acc - orig_class_acc
        
        status = "🔴" if i in [2, 4] else "🟢"  # 标记弱势类别
        print(f"  {status} {cn_name}: {orig_class_acc:.1%} → {impr_class_acc:.1%} ({class_improvement:+.1%})")
    
    # 重点关注弱势类别
    print(f"\n🎯 弱势类别改进重点:")
    weak_improvements = []
    for i, key in enumerate(['discomfort', 'tired']):
        orig_acc = original_results.get('class_accuracies', {}).get(key, 0)
        impr_acc = improved_results['class_accuracies'][key]
        improvement = impr_acc - orig_acc
        weak_improvements.append(improvement)
        
        cn_name = '不适' if key == 'discomfort' else '疲倦'
        print(f"  {cn_name}: {orig_acc:.1%} → {impr_acc:.1%} (提升 {improvement:+.1%})")
    
    # 评估改进效果
    avg_weak_improvement = np.mean(weak_improvements)
    if avg_weak_improvement > 0.1:  # 10%以上提升
        print(f"\n🎉 弱势类别显著改进！平均提升 {avg_weak_improvement:.1%}")
    elif avg_weak_improvement > 0.05:  # 5%以上提升
        print(f"\n👍 弱势类别有所改进，平均提升 {avg_weak_improvement:.1%}")
    else:
        print(f"\n⚠️ 弱势类别改进有限，可能需要更深层的优化")
    
    return improvement, weak_improvements

def main():
    """主函数"""
    print("🚀 快速改进方案1 - 针对弱势类别优化")
    print("=" * 80)
    print("策略：在现有模型基础上应用后处理增强，无需重新训练")
    print("目标：提升不适和疲倦类别识别准确率")
    print("=" * 80)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 查找原始模型文件
    possible_model_paths = [
        "stable_training_v1_results/best_stable_v1_model.pth",
        "stable_training_v1_results/stable_v1_full_model.pth",
        "best_stable_v1_model.pth"
    ]
    
    original_model = None
    for model_path in possible_model_paths:
        if Path(model_path).exists():
            original_model = load_original_model(model_path)
            if original_model is not None:
                break
    
    if original_model is None:
        print("❌ 未找到原始方案1模型，请先运行 stable_training_v1.py")
        return
    
    # 创建弱势类别增强器
    print("\n🔧 创建弱势类别增强器...")
    booster = WeakClassBooster(weak_classes=[2, 4], boost_factor=1.3)
    
    # 创建改进模型
    improved_model = ImprovedStableModel(original_model, booster)
    improved_model = improved_model.to(device)
    
    # 创建测试数据
    print("\n📊 准备测试数据...")
    data_dir = Path("Data")
    feature_extractor = StableFeatureExtractor()
    
    test_dataset = StableInfantCryDataset(
        data_dir, feature_extractor, split='test'
    )
    
    from torch.utils.data import DataLoader
    test_loader = DataLoader(test_dataset, batch_size=32, shuffle=False)
    
    # 评估改进模型
    print("\n🎯 评估改进效果...")
    improved_results = evaluate_improved_model(improved_model, test_loader, device)
    
    # 加载原始结果进行对比
    original_results = {}
    try:
        with open("comprehensive_performance_report.json", 'r', encoding='utf-8') as f:
            baseline_data = json.load(f)
        original_results = baseline_data['method_results']['方案1 (GPU稳定训练)']
        
        # 转换格式以便对比
        original_results['class_accuracies'] = {}
        for class_name, class_data in original_results['dir_accuracies'].items():
            original_results['class_accuracies'][class_name] = class_data['accuracy']
            
    except FileNotFoundError:
        print("⚠️ 未找到原始结果文件，无法进行详细对比")
    
    # 对比结果
    improvement, weak_improvements = compare_results(original_results, improved_results)
    
    # 保存改进结果
    output_dir = Path("quick_improved_results")
    output_dir.mkdir(exist_ok=True)
    
    result_data = {
        "method": "Quick Improved Solution 1",
        "overall_accuracy": improved_results['overall_accuracy'],
        "class_accuracies": improved_results['class_accuracies'],
        "improvement_over_original": improvement,
        "weak_class_improvements": {
            "discomfort": weak_improvements[0] if len(weak_improvements) > 0 else 0,
            "tired": weak_improvements[1] if len(weak_improvements) > 1 else 0
        },
        "total_samples": improved_results['total_samples'],
        "avg_inference_time": improved_results['avg_inference_time']
    }
    
    with open(output_dir / "quick_improvement_results.json", 'w', encoding='utf-8') as f:
        json.dump(result_data, f, indent=2, ensure_ascii=False)
    
    # 总结
    print(f"\n✅ 快速改进完成！")
    print(f"  总体准确率: {improved_results['overall_accuracy']:.1%}")
    print(f"  改进幅度: {improvement:+.1%}")
    print(f"  结果已保存到: {output_dir}")
    
    # 建议
    if improvement > 0.02:  # 2%以上提升
        print(f"\n🎉 快速改进效果显著！建议:")
        print(f"  1. 可以直接使用这个改进版本")
        print(f"  2. 考虑将增强策略集成到训练过程中")
    else:
        print(f"\n💡 快速改进效果有限，建议:")
        print(f"  1. 尝试运行完整的改进训练 (improved_solution1_training.py)")
        print(f"  2. 考虑数据增强和特征工程优化")
        print(f"  3. 与方案2集成学习结合使用")

if __name__ == "__main__":
    main()
