#!/usr/bin/env python3
"""
改进的 Core ML 分析工具

这个脚本展示了最佳实践的转换方法，即使在某些依赖不可用时也能工作。
它会尝试使用最佳实践方法，如果失败则回退到架构分析方法。

作者: DeepInfant Team
"""

import sys
import warnings
from pathlib import Path
import json
import numpy as np

# 基础依赖检查
try:
    import coremltools as ct
    COREMLTOOLS_AVAILABLE = True
except ImportError:
    COREMLTOOLS_AVAILABLE = False
    print("❌ coremltools 未安装")

try:
    import torch
    import torch.nn as nn
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False
    print("❌ torch 未安装")

try:
    import onnx
    ONNX_AVAILABLE = True
except ImportError:
    ONNX_AVAILABLE = False
    print("⚠️ onnx 未安装，将跳过 ONNX 转换")

# 可选依赖检查
try:
    from onnxsim import simplify
    ONNX_SIMPLIFIER_AVAILABLE = True
except ImportError:
    ONNX_SIMPLIFIER_AVAILABLE = False

try:
    from onnx2pytorch import ConvertModel
    ONNX2PYTORCH_AVAILABLE = True
except ImportError:
    ONNX2PYTORCH_AVAILABLE = False

def analyze_coreml_model(coreml_path):
    """分析 Core ML 模型的详细信息"""
    if not COREMLTOOLS_AVAILABLE:
        return {'status': 'error', 'message': 'coremltools 不可用'}
    
    try:
        print(f"📥 分析 Core ML 模型: {coreml_path}")
        mlmodel = ct.models.MLModel(str(coreml_path))
        spec = mlmodel.get_spec()
        
        analysis = {
            'model_path': str(coreml_path),
            'model_type': spec.WhichOneof('Type'),
            'specification_version': spec.specificationVersion,
            'inputs': [],
            'outputs': [],
            'layers': [],
            'class_labels': None
        }
        
        # 分析输入
        for input_desc in spec.description.input:
            input_info = {
                'name': input_desc.name,
                'type': input_desc.type.WhichOneof('Type')
            }
            
            if input_desc.type.HasField('multiArrayType'):
                array_type = input_desc.type.multiArrayType
                input_info.update({
                    'shape': list(array_type.shape),
                    'data_type': str(array_type.dataType)
                })
            
            analysis['inputs'].append(input_info)
        
        # 分析输出
        for output_desc in spec.description.output:
            output_info = {
                'name': output_desc.name,
                'type': output_desc.type.WhichOneof('Type')
            }
            
            if output_desc.type.HasField('multiArrayType'):
                array_type = output_desc.type.multiArrayType
                output_info.update({
                    'shape': list(array_type.shape),
                    'data_type': str(array_type.dataType)
                })
            
            analysis['outputs'].append(output_info)
        
        # 分析网络层
        nn_spec = None
        if spec.HasField('neuralNetwork'):
            nn_spec = spec.neuralNetwork
        elif spec.HasField('neuralNetworkClassifier'):
            nn_spec = spec.neuralNetworkClassifier
            # 提取类别标签
            if nn_spec.HasField('stringClassLabels'):
                analysis['class_labels'] = list(nn_spec.stringClassLabels.vector)
            elif nn_spec.HasField('int64ClassLabels'):
                analysis['class_labels'] = list(nn_spec.int64ClassLabels.vector)
        elif spec.HasField('pipelineClassifier'):
            pipeline_spec = spec.pipelineClassifier
            # 从 pipeline 中提取第一个神经网络
            for model in pipeline_spec.pipeline.models:
                if model.HasField('neuralNetwork'):
                    nn_spec = model.neuralNetwork
                    break
            # 提取类别标签
            try:
                if hasattr(pipeline_spec, 'stringClassLabels') and pipeline_spec.stringClassLabels.vector:
                    analysis['class_labels'] = list(pipeline_spec.stringClassLabels.vector)
                elif hasattr(pipeline_spec, 'int64ClassLabels') and pipeline_spec.int64ClassLabels.vector:
                    analysis['class_labels'] = list(pipeline_spec.int64ClassLabels.vector)
            except:
                pass
        
        if nn_spec:
            for layer in nn_spec.layers:
                layer_info = {
                    'name': layer.name,
                    'type': layer.WhichOneof('layer'),
                    'inputs': list(layer.input),
                    'outputs': list(layer.output)
                }
                
                layer_type = layer_info['type']
                if layer_type == 'convolution':
                    conv = layer.convolution
                    layer_info.update({
                        'output_channels': conv.outputChannels,
                        'kernel_size': list(conv.kernelSize),
                        'stride': list(conv.stride),
                        'padding_type': 'same' if not conv.valid else 'valid',
                        'has_bias': conv.hasBias
                    })
                elif layer_type == 'innerProduct':
                    fc = layer.innerProduct
                    layer_info.update({
                        'output_channels': fc.outputChannels,
                        'has_bias': fc.hasBias
                    })
                elif layer_type == 'pooling':
                    pool = layer.pooling
                    layer_info.update({
                        'type': str(pool.type),
                        'kernel_size': list(pool.kernelSize),
                        'stride': list(pool.stride)
                    })
                elif layer_type == 'activation':
                    act = layer.activation
                    layer_info['activation_type'] = act.WhichOneof('NonlinearityType')
                elif layer_type == 'batchnorm':
                    bn = layer.batchnorm
                    layer_info['channels'] = bn.channels
                
                analysis['layers'].append(layer_info)
        
        analysis['status'] = 'success'
        analysis['layer_count'] = len(analysis['layers'])
        analysis['num_classes'] = len(analysis['class_labels']) if analysis['class_labels'] else None
        
        return analysis
        
    except Exception as e:
        return {'status': 'error', 'error': str(e)}

def try_coreml_to_onnx_conversion(coreml_path, output_dir):
    """尝试 Core ML 到 ONNX 的转换"""
    if not (COREMLTOOLS_AVAILABLE and ONNX_AVAILABLE):
        return {'status': 'skipped', 'reason': 'dependencies_missing'}
    
    try:
        print(f"🔄 尝试 Core ML → ONNX 转换...")
        
        coreml_path = Path(coreml_path)
        output_dir = Path(output_dir)
        model_name = coreml_path.stem
        
        # 载入模型
        mlmodel = ct.models.MLModel(str(coreml_path))
        spec = mlmodel.get_spec()
        
        # 分析输入形状
        input_desc = spec.description.input[0]
        if input_desc.type.HasField('multiArrayType'):
            input_shape = list(input_desc.type.multiArrayType.shape)
        else:
            input_shape = [1, 80, 432]  # 默认形状
        
        # 创建输入类型
        if len(input_shape) == 1:
            tensor_shape = (1, 1, 80, 432)
        elif len(input_shape) == 3:
            tensor_shape = (1, *input_shape)
        else:
            tensor_shape = tuple(input_shape)
        
        tensor_input = ct.TensorType(shape=tensor_shape)
        
        # 执行转换
        onnx_model = ct.converters.convert(
            mlmodel,
            convert_to="onnx",
            minimum_deployment_target=ct.target.iOS15,
            inputs=[tensor_input]
        )
        
        # 保存 ONNX 模型
        onnx_path = output_dir / f"{model_name}_best_practice.onnx"
        onnx.save(onnx_model, str(onnx_path))
        
        print(f"   ✅ ONNX 转换成功: {onnx_path}")
        
        return {
            'status': 'success',
            'onnx_path': str(onnx_path),
            'input_shape': input_shape,
            'tensor_shape': tensor_shape
        }
        
    except Exception as e:
        print(f"   ❌ ONNX 转换失败: {e}")
        return {'status': 'error', 'error': str(e)}

def create_pytorch_model_from_analysis(analysis):
    """根据分析结果创建 PyTorch 模型（架构分析方法）"""
    if not TORCH_AVAILABLE:
        return None, {'status': 'error', 'message': 'torch 不可用'}
    
    try:
        num_classes = analysis.get('num_classes', 9)
        if num_classes is None or num_classes <= 0:
            num_classes = 9
        
        class AnalyzedDeepInfantModel(nn.Module):
            def __init__(self, num_classes):
                super().__init__()
                
                # 基于分析的层信息构建模型
                # 这里使用简化的架构作为示例
                self.features = nn.Sequential(
                    nn.Conv2d(1, 64, kernel_size=3, padding=1),
                    nn.ReLU(inplace=True),
                    nn.MaxPool2d(kernel_size=2, stride=2),
                    
                    nn.Conv2d(64, 128, kernel_size=3, padding=1),
                    nn.ReLU(inplace=True),
                    nn.MaxPool2d(kernel_size=2, stride=2),
                    
                    nn.Conv2d(128, 256, kernel_size=3, padding=1),
                    nn.ReLU(inplace=True),
                    nn.MaxPool2d(kernel_size=2, stride=2),
                )
                
                self.classifier = nn.Sequential(
                    nn.AdaptiveAvgPool2d((1, 1)),
                    nn.Flatten(),
                    nn.Linear(256, 512),
                    nn.ReLU(inplace=True),
                    nn.Dropout(0.3),
                    nn.Linear(512, num_classes)
                )
            
            def forward(self, x):
                x = self.features(x)
                x = self.classifier(x)
                return x
        
        model = AnalyzedDeepInfantModel(num_classes)
        return model, {'status': 'success', 'num_classes': num_classes}
        
    except Exception as e:
        return None, {'status': 'error', 'error': str(e)}

def main():
    print("🚀 改进的 Core ML 分析和转换工具")
    print("=" * 60)
    print("支持的转换方法:")
    print("1. 最佳实践: Core ML → ONNX (如果依赖可用)")
    print("2. 架构分析: 分析结构 + PyTorch 重建")
    print("=" * 60)
    
    # 检查依赖状态
    print("\n📋 依赖状态检查:")
    print(f"   coremltools: {'✅' if COREMLTOOLS_AVAILABLE else '❌'}")
    print(f"   torch: {'✅' if TORCH_AVAILABLE else '❌'}")
    print(f"   onnx: {'✅' if ONNX_AVAILABLE else '❌'}")
    print(f"   onnx-simplifier: {'✅' if ONNX_SIMPLIFIER_AVAILABLE else '❌'}")
    print(f"   onnx2pytorch: {'✅' if ONNX2PYTORCH_AVAILABLE else '❌'}")
    
    # 查找模型文件
    models_to_analyze = [
        "Models/DeepInfant_VGGish.mlmodel",
        "Models/DeepInfant_AFP.mlmodel",
        "iOS_iPadOS/deepinfant/model/DeepInfant_VGGish.mlmodel",
        "iOS_iPadOS/deepinfant/model/DeepInfant_AFP.mlmodel"
    ]
    
    existing_models = [model for model in models_to_analyze if Path(model).exists()]
    
    if not existing_models:
        print("\n❌ 未找到任何 Core ML 模型文件")
        return
    
    print(f"\n📁 找到 {len(existing_models)} 个模型文件:")
    for model in existing_models:
        print(f"   - {model}")
    
    # 创建输出目录
    output_dir = Path("improved_conversion_results")
    output_dir.mkdir(exist_ok=True)
    
    all_results = []
    
    for model_path in existing_models:
        print(f"\n{'='*60}")
        print(f"🔄 处理模型: {model_path}")
        
        model_result = {
            'model_path': model_path,
            'analysis': None,
            'onnx_conversion': None,
            'pytorch_model': None
        }
        
        # 1. 分析模型
        print(f"\n📋 步骤1: 模型分析")
        analysis = analyze_coreml_model(model_path)
        model_result['analysis'] = analysis
        
        if analysis['status'] == 'success':
            print(f"   ✅ 模型类型: {analysis['model_type']}")
            print(f"   ✅ 层数: {analysis['layer_count']}")
            print(f"   ✅ 类别数: {analysis['num_classes']}")
            if analysis['class_labels']:
                print(f"   ✅ 类别标签: {analysis['class_labels']}")
        
        # 2. 尝试 ONNX 转换
        print(f"\n📋 步骤2: ONNX 转换")
        onnx_result = try_coreml_to_onnx_conversion(model_path, output_dir)
        model_result['onnx_conversion'] = onnx_result
        
        # 3. 创建 PyTorch 模型
        if analysis['status'] == 'success':
            print(f"\n📋 步骤3: PyTorch 模型创建")
            pytorch_model, pytorch_result = create_pytorch_model_from_analysis(analysis)
            model_result['pytorch_model'] = pytorch_result
            
            if pytorch_result['status'] == 'success':
                # 测试模型
                test_input = torch.randn(1, 1, 80, 432)
                try:
                    with torch.no_grad():
                        output = pytorch_model(test_input)
                    print(f"   ✅ PyTorch 模型测试成功，输出形状: {output.shape}")
                    
                    # 保存模型
                    model_name = Path(model_path).stem
                    pytorch_path = output_dir / f"{model_name}_analyzed.pth"
                    torch.save(pytorch_model.state_dict(), pytorch_path)
                    print(f"   ✅ PyTorch 模型已保存: {pytorch_path}")
                    
                    model_result['pytorch_model']['saved_path'] = str(pytorch_path)
                    
                except Exception as e:
                    print(f"   ❌ PyTorch 模型测试失败: {e}")
                    model_result['pytorch_model']['test_error'] = str(e)
        
        all_results.append(model_result)
    
    # 保存结果报告
    report_path = output_dir / "improved_conversion_report.json"
    with open(report_path, 'w', encoding='utf-8') as f:
        json.dump(all_results, f, indent=2, ensure_ascii=False, default=str)
    
    # 显示总结
    print(f"\n{'='*60}")
    print("📊 转换总结")
    print("=" * 60)
    
    for result in all_results:
        model_name = Path(result['model_path']).stem
        analysis_status = result['analysis']['status'] if result['analysis'] else 'failed'
        onnx_status = result['onnx_conversion']['status'] if result['onnx_conversion'] else 'failed'
        pytorch_status = result['pytorch_model']['status'] if result['pytorch_model'] else 'failed'
        
        print(f"\n📁 {model_name}:")
        print(f"   分析: {'✅' if analysis_status == 'success' else '❌'}")
        print(f"   ONNX转换: {'✅' if onnx_status == 'success' else '❌' if onnx_status == 'error' else '⚠️'}")
        print(f"   PyTorch模型: {'✅' if pytorch_status == 'success' else '❌'}")
    
    print(f"\n📋 详细报告已保存: {report_path}")
    print(f"📁 输出目录: {output_dir}")
    
    # 改进建议
    print(f"\n💡 改进建议:")
    if not ONNX_AVAILABLE:
        print("   - 安装 onnx: pip install onnx")
    if not ONNX_SIMPLIFIER_AVAILABLE:
        print("   - 安装 onnx-simplifier: pip install onnx-simplifier")
    if not ONNX2PYTORCH_AVAILABLE:
        print("   - 安装 onnx2pytorch: pip install onnx2pytorch")
    
    print("\n🎯 使用最佳实践转换可以获得更好的结果（保留权重信息）")

if __name__ == "__main__":
    main()
