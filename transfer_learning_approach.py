#!/usr/bin/env python3
"""
迁移学习方案 - 使用预训练的音频分类模型

这个方案尝试使用预训练的音频分类模型作为特征提取器，
然后在婴儿哭声数据上进行微调。

作者: DeepInfant Team
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader, WeightedRandomSampler
import torchaudio
import torchaudio.transforms as T
import librosa
import numpy as np
from pathlib import Path
import json
from collections import Counter
from sklearn.model_selection import train_test_split
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

class PretrainedFeatureExtractor:
    """预训练特征提取器"""
    
    def __init__(self):
        # 尝试使用torchaudio的预训练模型
        try:
            # 使用Wav2Vec2作为特征提取器
            bundle = torchaudio.pipelines.WAV2VEC2_BASE
            self.model = bundle.get_model()
            self.model.eval()
            self.sample_rate = bundle.sample_rate
            self.available = True
            print("✅ 成功加载Wav2Vec2预训练模型")
        except Exception as e:
            print(f"⚠️ 预训练模型加载失败: {e}")
            self.available = False
    
    def extract_features(self, audio_path):
        """提取预训练特征"""
        if self.available:
            return self._extract_wav2vec2_features(audio_path)
        else:
            return self._extract_advanced_features(audio_path)
    
    def _extract_wav2vec2_features(self, audio_path):
        """使用Wav2Vec2提取特征"""
        try:
            # 加载音频
            waveform, sr = torchaudio.load(audio_path)
            
            # 重采样到模型要求的采样率
            if sr != self.sample_rate:
                resampler = T.Resample(sr, self.sample_rate)
                waveform = resampler(waveform)
            
            # 确保是单声道
            if waveform.shape[0] > 1:
                waveform = torch.mean(waveform, dim=0, keepdim=True)
            
            # 调整长度
            target_length = self.sample_rate * 3  # 3秒
            if waveform.shape[1] < target_length:
                waveform = torch.nn.functional.pad(waveform, (0, target_length - waveform.shape[1]))
            else:
                waveform = waveform[:, :target_length]
            
            # 提取特征
            with torch.no_grad():
                features, _ = self.model.extract_features(waveform)
                
                # 使用最后一层的特征
                last_layer_features = features[-1]  # Shape: [1, seq_len, feature_dim]
                
                # 计算统计特征
                features_mean = torch.mean(last_layer_features, dim=1)  # [1, feature_dim]
                features_std = torch.std(last_layer_features, dim=1)    # [1, feature_dim]
                features_max = torch.max(last_layer_features, dim=1)[0] # [1, feature_dim]
                features_min = torch.min(last_layer_features, dim=1)[0] # [1, feature_dim]
                
                # 拼接统计特征
                combined_features = torch.cat([
                    features_mean, features_std, features_max, features_min
                ], dim=1)
                
                return combined_features.squeeze(0).numpy()
                
        except Exception as e:
            print(f"Wav2Vec2特征提取失败: {e}")
            return self._extract_advanced_features(audio_path)
    
    def _extract_advanced_features(self, audio_path):
        """备用的高级特征提取"""
        try:
            audio, sr = librosa.load(audio_path, sr=16000)
            
            # 调整长度
            target_length = 16000 * 3
            if len(audio) < target_length:
                audio = np.pad(audio, (0, target_length - len(audio)), mode='constant')
            else:
                audio = audio[:target_length]
            
            features = []
            
            # 多尺度Mel频谱
            for n_mels in [64, 128, 256]:
                mel_spec = librosa.feature.melspectrogram(
                    y=audio, sr=sr, n_mels=n_mels, n_fft=1024, hop_length=512
                )
                mel_spec_db = librosa.power_to_db(mel_spec)
                
                features.extend([
                    np.mean(mel_spec_db, axis=1),
                    np.std(mel_spec_db, axis=1),
                    np.max(mel_spec_db, axis=1),
                    np.min(mel_spec_db, axis=1)
                ])
            
            # MFCC特征
            mfcc = librosa.feature.mfcc(y=audio, sr=sr, n_mfcc=26)
            features.extend([
                np.mean(mfcc, axis=1),
                np.std(mfcc, axis=1),
                np.max(mfcc, axis=1),
                np.min(mfcc, axis=1)
            ])
            
            # 色度特征
            chroma = librosa.feature.chroma_stft(y=audio, sr=sr)
            features.extend([
                np.mean(chroma, axis=1),
                np.std(chroma, axis=1)
            ])
            
            # 谱特征
            spectral_centroids = librosa.feature.spectral_centroid(y=audio, sr=sr)
            spectral_bandwidth = librosa.feature.spectral_bandwidth(y=audio, sr=sr)
            spectral_rolloff = librosa.feature.spectral_rolloff(y=audio, sr=sr)
            
            features.extend([
                np.mean(spectral_centroids), np.std(spectral_centroids),
                np.mean(spectral_bandwidth), np.std(spectral_bandwidth),
                np.mean(spectral_rolloff), np.std(spectral_rolloff)
            ])
            
            # 时域特征
            zcr = librosa.feature.zero_crossing_rate(audio)
            rms = librosa.feature.rms(y=audio)
            
            features.extend([
                np.mean(zcr), np.std(zcr),
                np.mean(rms), np.std(rms)
            ])
            
            # 展平并返回
            features = np.concatenate([f.flatten() for f in features])
            return features.astype(np.float32)
            
        except Exception as e:
            print(f"特征提取失败: {e}")
            return np.zeros(2000, dtype=np.float32)

class TransferLearningModel(nn.Module):
    """迁移学习模型"""
    
    def __init__(self, input_dim, num_classes=5, dropout=0.4):
        super(TransferLearningModel, self).__init__()
        
        # 特征映射层
        self.feature_mapping = nn.Sequential(
            nn.Linear(input_dim, 1024),
            nn.BatchNorm1d(1024),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout)
        )
        
        # 注意力机制
        self.attention = nn.MultiheadAttention(
            embed_dim=1024, num_heads=8, dropout=dropout, batch_first=True
        )
        
        # 分类器
        self.classifier = nn.Sequential(
            nn.Linear(1024, 512),
            nn.BatchNorm1d(512),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout),
            
            nn.Linear(512, 256),
            nn.BatchNorm1d(256),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout / 2),
            
            nn.Linear(256, 128),
            nn.BatchNorm1d(128),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout / 2),
            
            nn.Linear(128, num_classes)
        )
        
        self._initialize_weights()
    
    def _initialize_weights(self):
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.BatchNorm1d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
    
    def forward(self, x):
        # 特征映射
        features = self.feature_mapping(x)
        
        # 添加序列维度用于注意力机制
        features = features.unsqueeze(1)  # [batch_size, 1, 1024]
        
        # 自注意力
        attended_features, _ = self.attention(features, features, features)
        attended_features = attended_features.squeeze(1)  # [batch_size, 1024]
        
        # 分类
        output = self.classifier(attended_features)
        return output

class BalancedInfantCryDataset(Dataset):
    """平衡的婴儿哭声数据集"""
    
    def __init__(self, data_dir, feature_extractor, split='train', test_size=0.2):
        self.feature_extractor = feature_extractor
        self.label_map = {'bp': 0, 'bu': 1, 'dc': 2, 'hu': 3, 'ti': 4}
        self.label_names = {0: 'belly_pain', 1: 'burping', 2: 'discomfort', 3: 'hungry', 4: 'tired'}
        
        # 收集数据
        raw_data = self._collect_data(data_dir)
        
        # 平衡数据
        balanced_data = self._balance_data(raw_data)
        
        # 划分数据集
        if len(balanced_data) > 0:
            train_data, test_data = train_test_split(
                balanced_data, test_size=test_size, random_state=42, 
                stratify=[item[1] for item in balanced_data]
            )
            
            if split == 'train':
                self.data = train_data
            else:
                self.data = test_data
        else:
            self.data = []
        
        print(f"{split} 集大小: {len(self.data)}")
        if len(self.data) > 0:
            label_counts = Counter([item[1] for item in self.data])
            print(f"标签分布: {dict(label_counts)}")
    
    def _collect_data(self, data_dir):
        """收集数据"""
        data = []
        data_dir = Path(data_dir)
        
        dir_to_label = {
            'belly_pain': 'bp', 'burping': 'bu', 'discomfort': 'dc',
            'hungry': 'hu', 'tired': 'ti'
        }
        
        for dir_name, label in dir_to_label.items():
            dir_path = data_dir / dir_name
            if dir_path.exists():
                audio_files = list(dir_path.glob('*.wav'))
                for audio_file in audio_files:
                    data.append((str(audio_file), self.label_map[label]))
                print(f"从 {dir_name} 收集了 {len(audio_files)} 个文件")
        
        return data
    
    def _balance_data(self, data):
        """平衡数据 - 上采样少数类别"""
        label_counts = Counter([item[1] for item in data])
        max_count = max(label_counts.values())
        
        balanced_data = []
        for label in range(5):  # 5个类别
            label_data = [item for item in data if item[1] == label]
            current_count = len(label_data)
            
            if current_count == 0:
                continue
            
            # 计算需要重复的次数
            repeat_times = max_count // current_count
            remainder = max_count % current_count
            
            # 重复数据
            for _ in range(repeat_times):
                balanced_data.extend(label_data)
            
            # 添加剩余部分
            if remainder > 0:
                balanced_data.extend(label_data[:remainder])
        
        print(f"数据平衡: {len(data)} -> {len(balanced_data)}")
        return balanced_data
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        audio_path, label = self.data[idx]
        features = self.feature_extractor.extract_features(audio_path)
        return torch.FloatTensor(features), torch.LongTensor([label])[0]

def train_transfer_learning_model():
    """训练迁移学习模型"""
    print("🚀 迁移学习 + 预训练模型方案")
    print("=" * 60)
    
    # 设备选择
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 创建输出目录
    output_dir = Path("transfer_learning_results")
    output_dir.mkdir(exist_ok=True)
    
    # 初始化特征提取器
    feature_extractor = PretrainedFeatureExtractor()
    
    # 创建数据集
    train_dataset = BalancedInfantCryDataset('Data', feature_extractor, split='train')
    val_dataset = BalancedInfantCryDataset('Data', feature_extractor, split='test')
    
    if len(train_dataset) == 0:
        print("❌ 数据集为空")
        return
    
    # 获取特征维度
    sample_features, _ = train_dataset[0]
    input_dim = sample_features.shape[0]
    print(f"特征维度: {input_dim}")
    
    # 创建数据加载器
    train_loader = DataLoader(train_dataset, batch_size=32, shuffle=True, num_workers=2)
    val_loader = DataLoader(val_dataset, batch_size=32, shuffle=False, num_workers=2)
    
    # 创建模型
    model = TransferLearningModel(input_dim=input_dim, num_classes=5)
    model = model.to(device)
    
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 训练配置
    criterion = nn.CrossEntropyLoss(label_smoothing=0.1)
    optimizer = optim.AdamW(model.parameters(), lr=0.002, weight_decay=0.01)
    scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=50)
    
    # 训练循环
    best_val_acc = 0.0
    history = {'train_loss': [], 'train_acc': [], 'val_loss': [], 'val_acc': []}
    patience_counter = 0
    
    for epoch in range(150):
        # 训练
        model.train()
        train_loss = 0.0
        train_correct = 0
        train_total = 0
        
        train_pbar = tqdm(train_loader, desc=f'Epoch {epoch+1} [Train]')
        for features, labels in train_pbar:
            features, labels = features.to(device), labels.to(device)
            
            optimizer.zero_grad()
            outputs = model(features)
            loss = criterion(outputs, labels)
            loss.backward()
            
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()
            
            train_loss += loss.item()
            _, predicted = torch.max(outputs.data, 1)
            train_total += labels.size(0)
            train_correct += (predicted == labels).sum().item()
            
            train_pbar.set_postfix({
                'Loss': f'{loss.item():.4f}',
                'Acc': f'{100.*train_correct/train_total:.2f}%'
            })
        
        # 验证
        model.eval()
        val_loss = 0.0
        val_correct = 0
        val_total = 0
        
        with torch.no_grad():
            val_pbar = tqdm(val_loader, desc=f'Epoch {epoch+1} [Val]')
            for features, labels in val_pbar:
                features, labels = features.to(device), labels.to(device)
                outputs = model(features)
                loss = criterion(outputs, labels)
                
                val_loss += loss.item()
                _, predicted = torch.max(outputs.data, 1)
                val_total += labels.size(0)
                val_correct += (predicted == labels).sum().item()
                
                val_pbar.set_postfix({
                    'Loss': f'{loss.item():.4f}',
                    'Acc': f'{100.*val_correct/val_total:.2f}%'
                })
        
        # 计算指标
        avg_train_loss = train_loss / len(train_loader)
        avg_train_acc = 100. * train_correct / train_total
        avg_val_loss = val_loss / len(val_loader)
        avg_val_acc = 100. * val_correct / val_total
        
        # 记录历史
        history['train_loss'].append(avg_train_loss)
        history['train_acc'].append(avg_train_acc)
        history['val_loss'].append(avg_val_loss)
        history['val_acc'].append(avg_val_acc)
        
        # 学习率调度
        scheduler.step()
        
        # 保存最佳模型
        if avg_val_acc > best_val_acc:
            best_val_acc = avg_val_acc
            torch.save(model.state_dict(), output_dir / "best_transfer_model.pth")
            torch.save(model, output_dir / "transfer_full_model.pth")
            patience_counter = 0
        else:
            patience_counter += 1
        
        print(f'Epoch {epoch+1}: Train Acc: {avg_train_acc:.2f}%, Val Acc: {avg_val_acc:.2f}%, Best: {best_val_acc:.2f}%')
        
        # 早停
        if patience_counter >= 25:
            print("早停触发")
            break
    
    # 保存结果
    with open(output_dir / "transfer_results.json", 'w') as f:
        json.dump(history, f, indent=2)
    
    print(f"✅ 迁移学习模型训练完成！最佳验证准确率: {best_val_acc:.2f}%")
    return best_val_acc

if __name__ == "__main__":
    train_transfer_learning_model()
