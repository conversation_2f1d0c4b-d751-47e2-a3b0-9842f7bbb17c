#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
温和改进方案1 - 保持均衡性的同时提升弱势类别
================================================================================
基于现有方案1，通过数据增强和训练策略优化来改进弱势类别
保持原有架构，只优化训练过程
================================================================================
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader, WeightedRandomSampler
import numpy as np
from pathlib import Path
import json
from collections import Counter
from sklearn.model_selection import train_test_split
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

# 导入原有的稳定训练模块
from stable_training_v1 import StableFeatureExtractor, StableDeepInfantModel

class BalancedInfantCryDataset(Dataset):
    """平衡的婴儿哭声数据集 - 针对弱势类别进行适度增强"""
    
    def __init__(self, data_dir, feature_extractor, split='train', test_size=0.2, 
                 balance_weak_classes=True, random_state=42):
        self.feature_extractor = feature_extractor
        self.label_map = {'bp': 0, 'bu': 1, 'dc': 2, 'hu': 3, 'ti': 4}
        self.label_names = {0: 'belly_pain', 1: 'burping', 2: 'discomfort', 3: 'hungry', 4: 'tired'}
        self.balance_weak_classes = balance_weak_classes
        
        # 弱势类别
        self.weak_classes = [2, 4]  # discomfort, tired
        
        # 收集数据
        self.data = self._collect_data(data_dir)
        
        # 温和的数据平衡 - 只在训练时应用
        if balance_weak_classes and split == 'train':
            self.data = self._gentle_balance_data(self.data)
        
        # 划分数据集
        if len(self.data) > 0:
            train_data, test_data = train_test_split(
                self.data, test_size=test_size, random_state=random_state, 
                stratify=[item[1] for item in self.data]
            )
            
            if split == 'train':
                self.data = train_data
            else:
                self.data = test_data
        
        print(f"{split} 集大小: {len(self.data)}")
        if len(self.data) > 0:
            label_counts = Counter([item[1] for item in self.data])
            print(f"标签分布: {dict(label_counts)}")
    
    def _collect_data(self, data_dir):
        """收集数据"""
        data = []
        data_dir = Path(data_dir)
        
        dir_to_label = {
            'belly_pain': 'bp', 'burping': 'bu', 'discomfort': 'dc',
            'hungry': 'hu', 'tired': 'ti'
        }
        
        for dir_name, label in dir_to_label.items():
            dir_path = data_dir / dir_name
            if dir_path.exists():
                audio_files = list(dir_path.glob('*.wav'))
                for audio_file in audio_files:
                    data.append((str(audio_file), self.label_map[label]))
                print(f"从 {dir_name} 收集了 {len(audio_files)} 个文件")
        
        return data
    
    def _gentle_balance_data(self, data):
        """温和的数据平衡 - 适度增强弱势类别"""
        label_counts = Counter([item[1] for item in data])
        
        # 计算目标数量 - 不要过度增强
        target_counts = {}
        max_count = max(label_counts.values())
        
        for label, count in label_counts.items():
            if label in self.weak_classes:
                # 弱势类别增强到中等水平，不要达到最大
                target_counts[label] = min(count * 2, max_count // 2)
            else:
                target_counts[label] = count
        
        balanced_data = []
        for label, target_count in target_counts.items():
            label_data = [item for item in data if item[1] == label]
            current_count = len(label_data)
            
            # 添加原始数据
            balanced_data.extend(label_data)
            
            # 如果需要增强
            if target_count > current_count:
                additional_needed = target_count - current_count
                # 重复采样
                for i in range(additional_needed):
                    balanced_data.append(label_data[i % current_count])
                
                print(f"弱势类别 {label} 温和增强: {current_count} → {target_count}")
        
        return balanced_data
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        audio_path, label = self.data[idx]
        features = self.feature_extractor.extract_features(audio_path)
        return torch.FloatTensor(features), torch.LongTensor([label])[0]

class WeightedCrossEntropyLoss(nn.Module):
    """加权交叉熵损失 - 给弱势类别更高权重"""
    
    def __init__(self, class_weights=None):
        super(WeightedCrossEntropyLoss, self).__init__()
        self.class_weights = class_weights
        
    def forward(self, inputs, targets):
        if self.class_weights is not None:
            return nn.functional.cross_entropy(inputs, targets, weight=self.class_weights)
        else:
            return nn.functional.cross_entropy(inputs, targets)

def create_gentle_weighted_sampler(dataset):
    """创建温和的加权采样器"""
    labels = [dataset[i][1].item() for i in range(len(dataset))]
    label_counts = Counter(labels)
    
    # 计算温和的类别权重
    total_samples = len(labels)
    class_weights = {}
    
    for label, count in label_counts.items():
        if label in [2, 4]:  # 弱势类别
            # 给弱势类别适度增加权重，不要过度
            class_weights[label] = total_samples / (count * 0.7)
        else:
            class_weights[label] = total_samples / count
    
    # 为每个样本分配权重
    sample_weights = [class_weights[label] for label in labels]
    
    return WeightedRandomSampler(
        weights=sample_weights,
        num_samples=len(sample_weights),
        replacement=True
    )

def train_gentle_improved_model(model, train_loader, val_loader, num_epochs=80, device='cuda'):
    """温和改进的训练函数"""
    
    # 计算类别权重
    train_labels = []
    for _, labels in train_loader:
        train_labels.extend(labels.tolist())
    
    label_counts = Counter(train_labels)
    total_samples = len(train_labels)
    
    # 温和的类别权重
    class_weights = []
    for i in range(5):
        if i in label_counts:
            weight = total_samples / (5 * label_counts[i])
            if i in [2, 4]:  # 弱势类别
                weight *= 1.5  # 适度增加权重
            class_weights.append(weight)
        else:
            class_weights.append(1.0)
    
    class_weights = torch.FloatTensor(class_weights).to(device)
    
    # 损失函数
    criterion = WeightedCrossEntropyLoss(class_weights)
    
    # 优化器 - 使用较小的学习率
    optimizer = optim.AdamW(model.parameters(), lr=0.0008, weight_decay=0.01)
    
    # 学习率调度器
    scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=num_epochs)
    
    # 训练历史
    history = {'train_loss': [], 'train_acc': [], 'val_loss': [], 'val_acc': []}
    
    best_val_acc = 0.0
    best_model_state = None
    patience_counter = 0
    early_stopping_patience = 25
    
    print(f"开始温和改进训练，设备: {device}")
    print(f"类别权重: {class_weights.cpu().numpy()}")
    
    for epoch in range(num_epochs):
        # 训练阶段
        model.train()
        train_loss = 0.0
        train_correct = 0
        train_total = 0
        
        train_pbar = tqdm(train_loader, desc=f'Epoch {epoch+1}/{num_epochs} [Train]')
        for features, labels in train_pbar:
            features, labels = features.to(device), labels.to(device)
            
            optimizer.zero_grad()
            outputs = model(features)
            loss = criterion(outputs, labels)
            loss.backward()
            
            # 温和的梯度裁剪
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=0.5)
            optimizer.step()
            
            train_loss += loss.item()
            _, predicted = torch.max(outputs.data, 1)
            train_total += labels.size(0)
            train_correct += (predicted == labels).sum().item()
            
            train_pbar.set_postfix({
                'Loss': f'{loss.item():.4f}',
                'Acc': f'{100.*train_correct/train_total:.2f}%'
            })
        
        # 验证阶段
        model.eval()
        val_loss = 0.0
        val_correct = 0
        val_total = 0
        class_correct = [0] * 5
        class_total = [0] * 5
        
        with torch.no_grad():
            val_pbar = tqdm(val_loader, desc=f'Epoch {epoch+1}/{num_epochs} [Val]')
            for features, labels in val_pbar:
                features, labels = features.to(device), labels.to(device)
                
                outputs = model(features)
                loss = criterion(outputs, labels)
                
                val_loss += loss.item()
                _, predicted = torch.max(outputs, 1)
                val_total += labels.size(0)
                val_correct += (predicted == labels).sum().item()
                
                # 统计各类别准确率
                for i in range(labels.size(0)):
                    label = labels[i].item()
                    class_total[label] += 1
                    if predicted[i] == labels[i]:
                        class_correct[label] += 1
                
                val_pbar.set_postfix({
                    'Loss': f'{loss.item():.4f}',
                    'Acc': f'{100.*val_correct/val_total:.2f}%'
                })
        
        # 计算指标
        avg_train_loss = train_loss / len(train_loader)
        avg_train_acc = 100. * train_correct / train_total
        avg_val_loss = val_loss / len(val_loader)
        avg_val_acc = 100. * val_correct / val_total
        
        # 记录历史
        history['train_loss'].append(avg_train_loss)
        history['train_acc'].append(avg_train_acc)
        history['val_loss'].append(avg_val_loss)
        history['val_acc'].append(avg_val_acc)
        
        # 学习率调度
        scheduler.step()
        
        # 打印各类别准确率
        class_names = ['腹痛', '打嗝', '不适', '饥饿', '疲倦']
        class_accs = []
        for i in range(5):
            if class_total[i] > 0:
                acc = 100. * class_correct[i] / class_total[i]
                class_accs.append(acc)
                print(f"{class_names[i]}: {acc:.1f}%", end=" ")
            else:
                class_accs.append(0)
                print(f"{class_names[i]}: N/A", end=" ")
        print()
        
        # 保存最佳模型
        if avg_val_acc > best_val_acc:
            best_val_acc = avg_val_acc
            best_model_state = model.state_dict().copy()
            patience_counter = 0
            print(f"🎉 新的最佳验证准确率: {best_val_acc:.2f}%")
        else:
            patience_counter += 1
        
        print(f'Epoch {epoch+1}: Train Acc: {avg_train_acc:.2f}%, Val Acc: {avg_val_acc:.2f}%, Best: {best_val_acc:.2f}%')
        
        # 早停
        if patience_counter >= early_stopping_patience:
            print("早停触发")
            break
    
    # 加载最佳模型
    if best_model_state is not None:
        model.load_state_dict(best_model_state)
    
    return model, history

def main():
    """主函数"""
    print("🌟 温和改进方案1 - 保持均衡性的优化")
    print("=" * 80)
    print("策略：保持原有架构，通过数据平衡和训练优化改进弱势类别")
    print("目标：在保持均衡性的前提下，适度提升不适和疲倦类别")
    print("=" * 80)
    
    # 设备配置
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 数据目录
    data_dir = Path("Data")
    output_dir = Path("gentle_improved_results")
    output_dir.mkdir(exist_ok=True)
    
    # 创建特征提取器
    print("\n🔧 创建特征提取器...")
    feature_extractor = StableFeatureExtractor()
    
    # 创建平衡数据集
    print("\n📊 创建平衡数据集...")
    train_dataset = BalancedInfantCryDataset(
        data_dir, feature_extractor, split='train', balance_weak_classes=True
    )
    val_dataset = BalancedInfantCryDataset(
        data_dir, feature_extractor, split='test', balance_weak_classes=False
    )
    
    # 获取特征维度
    sample_features, _ = train_dataset[0]
    input_dim = sample_features.shape[0]
    print(f"特征维度: {input_dim}")
    
    # 创建数据加载器
    train_sampler = create_gentle_weighted_sampler(train_dataset)
    train_loader = DataLoader(train_dataset, batch_size=32, sampler=train_sampler)
    val_loader = DataLoader(val_dataset, batch_size=32, shuffle=False)
    
    # 创建模型 - 使用原有架构
    print(f"\n🏗️ 创建模型...")
    model = StableDeepInfantModel(input_dim=input_dim, num_classes=5)
    model = model.to(device)
    
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 训练模型
    print("\n🎯 开始温和改进训练...")
    trained_model, history = train_gentle_improved_model(model, train_loader, val_loader, num_epochs=80, device=device)
    
    # 保存模型
    model_path = output_dir / "gentle_improved_model.pth"
    torch.save(trained_model.state_dict(), model_path)
    print(f"✅ 模型已保存: {model_path}")
    
    # 保存训练历史
    with open(output_dir / "gentle_improved_history.json", 'w') as f:
        json.dump(history, f, indent=2)
    
    print(f"✅ 温和改进训练完成！")
    return trained_model, history

if __name__ == "__main__":
    main()
