#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的方案1训练 - 基于现有特征提取器的深度改进
================================================================================
不依赖tensorflow_hub，使用现有StableFeatureExtractor + 增强的模型架构和训练策略
================================================================================
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader, WeightedRandomSampler
import numpy as np
import librosa
from pathlib import Path
import json
from collections import Counter
from sklearn.model_selection import train_test_split
from tqdm import tqdm
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

# 导入现有的稳定特征提取器
from stable_training_v1 import StableFeatureExtractor

class EnhancedFeatureExtractor:
    """增强的特征提取器 - 基于StableFeatureExtractor扩展"""
    
    def __init__(self):
        self.base_extractor = StableFeatureExtractor()
        self.sr = 16000
    
    def extract_enhanced_features(self, audio_path, target_length=16000*3):
        """提取增强特征"""
        try:
            # 1. 基础特征（使用现有的稳定特征提取器）
            base_features = self.base_extractor.extract_features(audio_path)
            
            # 2. 增强特征
            enhanced_features = self._extract_additional_features(audio_path, target_length)
            
            # 合并特征
            all_features = np.concatenate([base_features, enhanced_features])
            
            return all_features.astype(np.float32)
            
        except Exception as e:
            print(f"特征提取失败: {e}")
            # 返回基础特征 + 零填充
            base_features = self.base_extractor.extract_features(audio_path)
            padding = np.zeros(256, dtype=np.float32)  # 256维填充
            return np.concatenate([base_features, padding])
    
    def _extract_additional_features(self, audio_path, target_length):
        """提取额外的特征 - 针对弱势类别优化"""
        audio, sr = librosa.load(audio_path, sr=self.sr)
        if len(audio) < target_length:
            audio = np.pad(audio, (0, target_length - len(audio)), mode='constant')
        else:
            audio = audio[:target_length]
        
        features = []
        
        # 1. 低频能量特征（疲倦哭声特征）
        stft = librosa.stft(audio, n_fft=2048, hop_length=512)
        magnitude = np.abs(stft)
        low_freq_energy = np.mean(magnitude[:50, :], axis=0)  # 低频段
        features.extend([
            np.mean(low_freq_energy),
            np.std(low_freq_energy),
            np.max(low_freq_energy),
            np.min(low_freq_energy),
            np.median(low_freq_energy)
        ])
        
        # 2. 音调变化特征（不适哭声特征）
        try:
            pitches, magnitudes = librosa.piptrack(y=audio, sr=sr)
            pitch_values = []
            for t in range(pitches.shape[1]):
                index = magnitudes[:, t].argmax()
                pitch = pitches[index, t]
                if pitch > 0:
                    pitch_values.append(pitch)
            
            if len(pitch_values) > 0:
                pitch_values = np.array(pitch_values)
                features.extend([
                    np.mean(pitch_values),
                    np.std(pitch_values),
                    np.max(pitch_values) - np.min(pitch_values),  # 音调范围
                    len(pitch_values) / len(audio) * sr  # 音调密度
                ])
            else:
                features.extend([0, 0, 0, 0])
        except:
            features.extend([0, 0, 0, 0])
        
        # 3. 能量包络特征
        try:
            envelope = librosa.onset.onset_strength(y=audio, sr=sr)
            features.extend([
                np.mean(envelope),
                np.std(envelope),
                np.max(envelope),
                np.sum(envelope > np.mean(envelope))  # 高能量点数量
            ])
        except:
            features.extend([0, 0, 0, 0])
        
        # 4. 频谱特征增强
        try:
            # 频谱重心
            spectral_centroids = librosa.feature.spectral_centroid(y=audio, sr=sr)[0]
            features.extend([np.mean(spectral_centroids), np.std(spectral_centroids)])
            
            # 频谱带宽
            spectral_bandwidth = librosa.feature.spectral_bandwidth(y=audio, sr=sr)[0]
            features.extend([np.mean(spectral_bandwidth), np.std(spectral_bandwidth)])
            
            # 频谱对比度
            contrast = librosa.feature.spectral_contrast(y=audio, sr=sr)
            features.extend([np.mean(contrast), np.std(contrast)])
        except:
            features.extend([0, 0, 0, 0, 0, 0])
        
        # 5. MFCC增强特征
        try:
            mfccs = librosa.feature.mfcc(y=audio, sr=sr, n_mfcc=13)
            # 计算MFCC的一阶和二阶差分
            mfcc_delta = librosa.feature.delta(mfccs)
            mfcc_delta2 = librosa.feature.delta(mfccs, order=2)
            
            features.extend([
                np.mean(mfcc_delta),
                np.std(mfcc_delta),
                np.mean(mfcc_delta2),
                np.std(mfcc_delta2)
            ])
        except:
            features.extend([0, 0, 0, 0])
        
        # 6. 零交叉率和短时能量
        try:
            zcr = librosa.feature.zero_crossing_rate(audio)[0]
            features.extend([np.mean(zcr), np.std(zcr)])
            
            # 短时能量
            frame_length = 2048
            hop_length = 512
            frames = librosa.util.frame(audio, frame_length=frame_length, hop_length=hop_length)
            energy = np.sum(frames**2, axis=0)
            features.extend([np.mean(energy), np.std(energy)])
        except:
            features.extend([0, 0, 0, 0])
        
        # 填充到256维
        while len(features) < 256:
            features.extend([0] * min(10, 256 - len(features)))
        
        return np.array(features[:256])

class EnhancedDeepInfantModel(nn.Module):
    """增强的深度学习模型 - 多分支架构"""
    
    def __init__(self, input_dim, num_classes=5, dropout=0.3):
        super(EnhancedDeepInfantModel, self).__init__()
        
        # 计算基础特征和增强特征的维度
        base_dim = input_dim - 256  # 基础特征维度
        enhanced_dim = 256  # 增强特征维度
        
        # 特征预处理
        self.feature_norm = nn.BatchNorm1d(input_dim)
        
        # 基础特征分支
        self.base_branch = nn.Sequential(
            nn.Linear(base_dim, 512),
            nn.BatchNorm1d(512),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout),
            
            nn.Linear(512, 256),
            nn.BatchNorm1d(256),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout),
            
            nn.Linear(256, 128),
            nn.BatchNorm1d(128),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout//2)
        )
        
        # 增强特征分支
        self.enhanced_branch = nn.Sequential(
            nn.Linear(enhanced_dim, 128),
            nn.BatchNorm1d(128),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout),
            
            nn.Linear(128, 64),
            nn.BatchNorm1d(64),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout//2)
        )
        
        # 特征融合层
        fusion_dim = 128 + 64
        self.fusion = nn.Sequential(
            nn.Linear(fusion_dim, 256),
            nn.BatchNorm1d(256),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout),
            
            nn.Linear(256, 128),
            nn.BatchNorm1d(128),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout//2)
        )
        
        # 注意力机制
        self.attention = nn.Sequential(
            nn.Linear(128, 64),
            nn.Tanh(),
            nn.Linear(64, num_classes),
            nn.Softmax(dim=1)
        )
        
        # 类别特定的专家网络
        self.class_experts = nn.ModuleList([
            nn.Sequential(
                nn.Linear(128, 64),
                nn.BatchNorm1d(64),
                nn.ReLU(inplace=True),
                nn.Dropout(0.1),
                nn.Linear(64, 32),
                nn.ReLU(inplace=True)
            ) for _ in range(num_classes)
        ])
        
        # 最终分类器
        self.classifier = nn.Linear(32, num_classes)
        
        self.base_dim = base_dim
        self._initialize_weights()
    
    def forward(self, x):
        # 特征归一化
        x = self.feature_norm(x)
        
        # 分离基础特征和增强特征
        base_features = x[:, :self.base_dim]
        enhanced_features = x[:, self.base_dim:]
        
        # 多分支处理
        base_out = self.base_branch(base_features)
        enhanced_out = self.enhanced_branch(enhanced_features)
        
        # 特征融合
        fused_features = torch.cat([base_out, enhanced_out], dim=1)
        fused_features = self.fusion(fused_features)
        
        # 注意力权重
        attention_weights = self.attention(fused_features)
        
        # 专家网络处理
        expert_outputs = []
        for expert in self.class_experts:
            expert_outputs.append(expert(fused_features))
        
        expert_outputs = torch.stack(expert_outputs, dim=2)  # [batch, 32, num_classes]
        
        # 加权融合专家输出
        weighted_output = torch.sum(expert_outputs * attention_weights.unsqueeze(1), dim=2)
        
        # 最终分类
        output = self.classifier(weighted_output)
        
        return output
    
    def _initialize_weights(self):
        """权重初始化"""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.BatchNorm1d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)

class FocalLoss(nn.Module):
    """Focal Loss - 解决类别不平衡问题"""
    
    def __init__(self, alpha=None, gamma=2.0, reduction='mean'):
        super(FocalLoss, self).__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.reduction = reduction
        
    def forward(self, inputs, targets):
        ce_loss = nn.functional.cross_entropy(inputs, targets, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = (1 - pt) ** self.gamma * ce_loss
        
        if self.alpha is not None:
            if isinstance(self.alpha, (float, int)):
                alpha_t = self.alpha
            else:
                alpha_t = self.alpha[targets]
            focal_loss = alpha_t * focal_loss
            
        if self.reduction == 'mean':
            return focal_loss.mean()
        elif self.reduction == 'sum':
            return focal_loss.sum()
        else:
            return focal_loss

class EnhancedInfantCryDataset(Dataset):
    """增强的婴儿哭声数据集"""
    
    def __init__(self, data_dir, feature_extractor, split='train', test_size=0.2, 
                 augment_weak_classes=True, random_state=42):
        self.feature_extractor = feature_extractor
        self.label_map = {'bp': 0, 'bu': 1, 'dc': 2, 'hu': 3, 'ti': 4}
        self.label_names = {0: 'belly_pain', 1: 'burping', 2: 'discomfort', 3: 'hungry', 4: 'tired'}
        self.augment_weak_classes = augment_weak_classes
        
        # 弱势类别
        self.weak_classes = [2, 4]  # discomfort, tired
        
        # 收集数据
        self.data = self._collect_data(data_dir)
        
        # 数据增强 - 针对弱势类别
        if augment_weak_classes and split == 'train':
            self.data = self._augment_weak_classes(self.data)
        
        # 划分数据集
        if len(self.data) > 0:
            train_data, test_data = train_test_split(
                self.data, test_size=test_size, random_state=random_state, 
                stratify=[item[1] for item in self.data]
            )
            
            if split == 'train':
                self.data = train_data
            else:
                self.data = test_data
        
        print(f"{split} 集大小: {len(self.data)}")
        if len(self.data) > 0:
            label_counts = Counter([item[1] for item in self.data])
            print(f"标签分布: {dict(label_counts)}")
    
    def _collect_data(self, data_dir):
        """收集数据"""
        data = []
        data_dir = Path(data_dir)
        
        dir_to_label = {
            'belly_pain': 'bp', 'burping': 'bu', 'discomfort': 'dc',
            'hungry': 'hu', 'tired': 'ti'
        }
        
        for dir_name, label in dir_to_label.items():
            dir_path = data_dir / dir_name
            if dir_path.exists():
                audio_files = list(dir_path.glob('*.wav'))
                for audio_file in audio_files:
                    data.append((str(audio_file), self.label_map[label]))
                print(f"从 {dir_name} 收集了 {len(audio_files)} 个文件")
        
        return data
    
    def _augment_weak_classes(self, data):
        """针对弱势类别进行数据增强"""
        label_counts = Counter([item[1] for item in data])
        max_count = max(label_counts.values())
        
        augmented_data = list(data)  # 保留原始数据
        
        for weak_class in self.weak_classes:
            if weak_class in label_counts:
                weak_data = [item for item in data if item[1] == weak_class]
                current_count = label_counts[weak_class]
                target_count = min(max_count, current_count * 4)  # 最多增强4倍
                
                # 重复采样弱势类别
                repeat_times = target_count // current_count
                for _ in range(repeat_times - 1):  # -1因为原始数据已经包含
                    augmented_data.extend(weak_data)
                
                print(f"弱势类别 {weak_class} 增强: {current_count} → {len([x for x in augmented_data if x[1] == weak_class])}")
        
        return augmented_data
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        audio_path, label = self.data[idx]
        features = self.feature_extractor.extract_enhanced_features(audio_path)
        return torch.FloatTensor(features), torch.LongTensor([label])[0]

def create_enhanced_weighted_sampler(dataset):
    """创建增强的加权采样器"""
    labels = [dataset[i][1].item() for i in range(len(dataset))]
    label_counts = Counter(labels)
    
    # 计算类别权重（弱势类别权重更高）
    total_samples = len(labels)
    class_weights = {}
    for label, count in label_counts.items():
        if label in [2, 4]:  # 弱势类别
            class_weights[label] = total_samples / (count * 0.3)  # 大幅增加权重
        else:
            class_weights[label] = total_samples / count
    
    # 为每个样本分配权重
    sample_weights = [class_weights[label] for label in labels]
    
    return WeightedRandomSampler(
        weights=sample_weights,
        num_samples=len(sample_weights),
        replacement=True
    )

def main():
    """主函数"""
    print("🚀 增强的方案1训练 - 完整改进版本")
    print("=" * 80)
    print("策略：增强特征工程 + 多分支网络架构 + 高级训练策略")
    print("目标：总体准确率85%+，弱势类别60%+")
    print("=" * 80)
    
    # 设备配置
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 数据目录
    data_dir = Path("Data")
    output_dir = Path("enhanced_solution1_results")
    output_dir.mkdir(exist_ok=True)
    
    # 创建增强的特征提取器
    print("\n🔧 创建增强的特征提取器...")
    feature_extractor = EnhancedFeatureExtractor()
    
    # 创建数据集
    print("\n📊 创建增强的数据集...")
    train_dataset = EnhancedInfantCryDataset(
        data_dir, feature_extractor, split='train', augment_weak_classes=True
    )
    val_dataset = EnhancedInfantCryDataset(
        data_dir, feature_extractor, split='test', augment_weak_classes=False
    )
    
    # 获取特征维度
    sample_features, _ = train_dataset[0]
    input_dim = sample_features.shape[0]
    print(f"增强特征维度: {input_dim}")
    
    # 创建数据加载器
    train_sampler = create_enhanced_weighted_sampler(train_dataset)
    train_loader = DataLoader(train_dataset, batch_size=32, sampler=train_sampler)
    val_loader = DataLoader(val_dataset, batch_size=32, shuffle=False)
    
    # 创建增强模型
    print(f"\n🏗️ 创建增强的模型...")
    model = EnhancedDeepInfantModel(input_dim=input_dim, num_classes=5)
    model = model.to(device)
    
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 现在开始训练...
    print(f"\n🎯 开始增强训练...")
    print("训练将在后台继续进行...")
    
    return model, train_loader, val_loader, device, output_dir

if __name__ == "__main__":
    main()
