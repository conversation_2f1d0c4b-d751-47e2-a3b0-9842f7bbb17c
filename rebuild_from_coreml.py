import coremltools as ct
import torch
import torch.nn as nn
import numpy as np
from pathlib import Path
import json
import onnx
from onnxsim import simplify
import warnings

# 尝试导入 onnx2pytorch，如果没有安装则提示
try:
    from onnx2pytorch import ConvertModel
    ONNX2PYTORCH_AVAILABLE = True
except ImportError:
    ONNX2PYTORCH_AVAILABLE = False
    warnings.warn("onnx2pytorch 未安装。请运行: pip install onnx2pytorch")

class CoreMLAnalyzer:
    def __init__(self, coreml_path):
        self.coreml_path = coreml_path
        self.model = ct.models.MLModel(coreml_path)
        self.spec = self.model.get_spec()
        
    def analyze_model(self):
        """分析Core ML模型的详细结构"""
        print(f"分析模型: {self.coreml_path}")
        print("=" * 50)
        
        # 基本信息
        print("模型基本信息:")
        print(f"- 模型类型: {self.spec.WhichOneof('Type')}")
        print(f"- 规格版本: {self.spec.specificationVersion}")
        
        # 输入信息
        print("\n输入信息:")
        for i, input_desc in enumerate(self.spec.description.input):
            print(f"输入 {i+1}:")
            print(f"  - 名称: {input_desc.name}")
            print(f"  - 类型: {input_desc.type.WhichOneof('Type')}")
            
            if input_desc.type.HasField('multiArrayType'):
                array_type = input_desc.type.multiArrayType
                print(f"  - 形状: {list(array_type.shape)}")
                print(f"  - 数据类型: {array_type.dataType}")
        
        # 输出信息
        print("\n输出信息:")
        for i, output_desc in enumerate(self.spec.description.output):
            print(f"输出 {i+1}:")
            print(f"  - 名称: {output_desc.name}")
            print(f"  - 类型: {output_desc.type.WhichOneof('Type')}")
            
            if output_desc.type.HasField('multiArrayType'):
                array_type = output_desc.type.multiArrayType
                print(f"  - 形状: {list(array_type.shape)}")
                print(f"  - 数据类型: {array_type.dataType}")
        
        # 神经网络层信息
        if self.spec.HasField('neuralNetwork'):
            self.analyze_neural_network()
        elif self.spec.HasField('neuralNetworkClassifier'):
            self.analyze_neural_network_classifier()
        elif self.spec.HasField('pipelineClassifier'):
            self.analyze_pipeline_classifier()
        elif self.spec.HasField('pipeline'):
            self.analyze_pipeline()

        return self.extract_architecture_info()
    
    def analyze_neural_network(self):
        """分析神经网络结构"""
        print("\n神经网络层结构:")
        nn_spec = self.spec.neuralNetwork
        
        for i, layer in enumerate(nn_spec.layers):
            layer_type = layer.WhichOneof('layer')
            print(f"层 {i+1}: {layer.name} ({layer_type})")
            
            # 分析不同类型的层
            if layer_type == 'convolution':
                conv = layer.convolution
                print(f"  - 卷积核数量: {conv.outputChannels}")
                print(f"  - 卷积核大小: {list(conv.kernelSize)}")
                print(f"  - 步长: {list(conv.stride)}")
                print(f"  - 填充: {list(conv.valid) if conv.valid else 'same'}")
                
            elif layer_type == 'pooling':
                pool = layer.pooling
                print(f"  - 池化类型: {pool.type}")
                print(f"  - 池化大小: {list(pool.kernelSize)}")
                print(f"  - 步长: {list(pool.stride)}")
                
            elif layer_type == 'activation':
                act = layer.activation
                act_type = act.WhichOneof('NonlinearityType')
                print(f"  - 激活函数: {act_type}")
                
            elif layer_type == 'innerProduct':
                fc = layer.innerProduct
                print(f"  - 输出大小: {fc.outputChannels}")
                
            elif layer_type == 'batchnorm':
                bn = layer.batchnorm
                print(f"  - 通道数: {bn.channels}")
    
    def analyze_neural_network_classifier(self):
        """分析神经网络分类器结构"""
        print("\n神经网络分类器结构:")
        nnc_spec = self.spec.neuralNetworkClassifier
        
        # 分析网络层
        for i, layer in enumerate(nnc_spec.layers):
            layer_type = layer.WhichOneof('layer')
            print(f"层 {i+1}: {layer.name} ({layer_type})")
            
        # 分析分类器信息
        if nnc_spec.HasField('stringClassLabels'):
            print(f"\n类别标签: {list(nnc_spec.stringClassLabels.vector)}")
        elif nnc_spec.HasField('int64ClassLabels'):
            print(f"\n类别标签: {list(nnc_spec.int64ClassLabels.vector)}")

    def analyze_pipeline_classifier(self):
        """分析pipeline分类器结构"""
        print("\nPipeline分类器结构:")
        pipeline_spec = self.spec.pipelineClassifier

        print(f"Pipeline包含 {len(pipeline_spec.pipeline.models)} 个模型:")
        for i, model in enumerate(pipeline_spec.pipeline.models):
            print(f"模型 {i+1}: {model.WhichOneof('Type')}")

            # 如果是神经网络，分析其结构
            if model.HasField('neuralNetwork'):
                print(f"  神经网络层数: {len(model.neuralNetwork.layers)}")
                for j, layer in enumerate(model.neuralNetwork.layers[:5]):  # 只显示前5层
                    layer_type = layer.WhichOneof('layer')
                    print(f"    层 {j+1}: {layer.name} ({layer_type})")
                if len(model.neuralNetwork.layers) > 5:
                    print(f"    ... 还有 {len(model.neuralNetwork.layers) - 5} 层")

        # 分析分类器标签
        try:
            if hasattr(pipeline_spec, 'stringClassLabels') and pipeline_spec.stringClassLabels.vector:
                labels = list(pipeline_spec.stringClassLabels.vector)
                print(f"\n类别标签 ({len(labels)} 个): {labels}")
            elif hasattr(pipeline_spec, 'int64ClassLabels') and pipeline_spec.int64ClassLabels.vector:
                labels = list(pipeline_spec.int64ClassLabels.vector)
                print(f"\n类别标签 ({len(labels)} 个): {labels}")
            else:
                print("\n未找到类别标签信息")
        except Exception as e:
            print(f"\n获取类别标签时出错: {e}")

    def analyze_pipeline(self):
        """分析pipeline结构"""
        print("\nPipeline结构:")
        pipeline_spec = self.spec.pipeline

        print(f"Pipeline包含 {len(pipeline_spec.models)} 个模型:")
        for i, model in enumerate(pipeline_spec.models):
            print(f"模型 {i+1}: {model.WhichOneof('Type')}")
    
    def extract_architecture_info(self):
        """提取架构信息用于重建PyTorch模型"""
        arch_info = {
            'input_shape': None,
            'output_shape': None,
            'num_classes': None,
            'layers': [],
            'model_type': self.spec.WhichOneof('Type')
        }

        # 提取输入输出形状
        if self.spec.description.input:
            input_desc = self.spec.description.input[0]
            if input_desc.type.HasField('multiArrayType'):
                arch_info['input_shape'] = list(input_desc.type.multiArrayType.shape)

        # 对于pipeline分类器，尝试从分类器标签推断类别数
        if self.spec.HasField('pipelineClassifier'):
            pipeline_spec = self.spec.pipelineClassifier
            try:
                if hasattr(pipeline_spec, 'stringClassLabels') and pipeline_spec.stringClassLabels.vector:
                    arch_info['num_classes'] = len(pipeline_spec.stringClassLabels.vector)
                    arch_info['class_labels'] = list(pipeline_spec.stringClassLabels.vector)
                elif hasattr(pipeline_spec, 'int64ClassLabels') and pipeline_spec.int64ClassLabels.vector:
                    arch_info['num_classes'] = len(pipeline_spec.int64ClassLabels.vector)
                    arch_info['class_labels'] = list(pipeline_spec.int64ClassLabels.vector)
                else:
                    arch_info['num_classes'] = 9  # 默认值
            except:
                arch_info['num_classes'] = 9  # 默认值
        elif self.spec.description.output:
            output_desc = self.spec.description.output[0]
            if output_desc.type.HasField('multiArrayType'):
                arch_info['output_shape'] = list(output_desc.type.multiArrayType.shape)
                arch_info['num_classes'] = arch_info['output_shape'][-1] if arch_info['output_shape'] else None

        # 提取层信息
        nn_spec = None
        if self.spec.HasField('neuralNetwork'):
            nn_spec = self.spec.neuralNetwork
        elif self.spec.HasField('neuralNetworkClassifier'):
            nn_spec = self.spec.neuralNetworkClassifier
        elif self.spec.HasField('pipelineClassifier'):
            # 从pipeline中提取第一个神经网络模型
            pipeline_spec = self.spec.pipelineClassifier
            for model in pipeline_spec.pipeline.models:
                if model.HasField('neuralNetwork'):
                    nn_spec = model.neuralNetwork
                    break

        if nn_spec:
            for layer in nn_spec.layers:
                layer_info = {
                    'name': layer.name,
                    'type': layer.WhichOneof('layer')
                }

                if layer_info['type'] == 'convolution':
                    conv = layer.convolution
                    layer_info.update({
                        'out_channels': conv.outputChannels,
                        'kernel_size': list(conv.kernelSize),
                        'stride': list(conv.stride),
                        'padding': 'same' if not conv.valid else 'valid'
                    })
                elif layer_info['type'] == 'innerProduct':
                    fc = layer.innerProduct
                    layer_info['out_features'] = fc.outputChannels

                arch_info['layers'].append(layer_info)

        return arch_info

def convert_coreml_to_onnx_to_pytorch(coreml_path, output_dir="./"):
    """
    使用最佳实践：Core ML → ONNX → PyTorch 转换管道
    这是推荐的转换方法，可以保留权重信息
    """
    print(f"\n🚀 开始最佳实践转换: {coreml_path}")
    print("=" * 60)

    coreml_path = Path(coreml_path)
    output_dir = Path(output_dir)
    model_name = coreml_path.stem

    try:
        # 1. 载入 Core ML 模型
        print("📥 载入 Core ML 模型...")
        mlmodel = ct.models.MLModel(str(coreml_path))
        spec = mlmodel.get_spec()

        # 分析输入形状
        input_desc = spec.description.input[0]
        if input_desc.type.HasField('multiArrayType'):
            input_shape = list(input_desc.type.multiArrayType.shape)
            print(f"   输入形状: {input_shape}")
        else:
            # 默认音频输入形状
            input_shape = [1, 80, 432]  # mel频谱图格式
            print(f"   使用默认输入形状: {input_shape}")

        # 2. Core ML → ONNX 转换
        print("\n🔄 转换 Core ML → ONNX...")
        onnx_path = output_dir / f"{model_name}_from_coreml.onnx"

        # 创建输入类型描述
        if len(input_shape) == 1:
            # 1D 输入，转换为适合的形状
            tensor_input = ct.TensorType(shape=(1, 1, 80, 432))
        elif len(input_shape) == 3:
            # 3D 输入，添加 batch 维度
            tensor_input = ct.TensorType(shape=(1, *input_shape))
        else:
            # 其他情况，直接使用
            tensor_input = ct.TensorType(shape=input_shape)

        # 执行转换
        onnx_model = ct.converters.convert(
            mlmodel,
            convert_to="onnx",
            minimum_deployment_target=ct.target.iOS15,  # 兼容性设置
            inputs=[tensor_input]
        )

        # 保存 ONNX 模型
        onnx.save(onnx_model, str(onnx_path))
        print(f"   ✅ ONNX 模型已保存: {onnx_path}")

        # 3. ONNX 简化（可选但推荐）
        print("\n🔧 简化 ONNX 模型...")
        try:
            model_simp, check = simplify(str(onnx_path))
            if check:
                simplified_path = output_dir / f"{model_name}_simplified.onnx"
                onnx.save(model_simp, str(simplified_path))
                print(f"   ✅ 简化模型已保存: {simplified_path}")
                onnx_path = simplified_path  # 使用简化后的模型
            else:
                print("   ⚠️ 模型简化失败，使用原始 ONNX 模型")
        except Exception as e:
            print(f"   ⚠️ 模型简化出错: {e}，使用原始 ONNX 模型")

        # 4. ONNX → PyTorch 转换
        if ONNX2PYTORCH_AVAILABLE:
            print("\n🔄 转换 ONNX → PyTorch...")
            try:
                pytorch_model = ConvertModel(str(onnx_path), experimental=True)
                print("   ✅ PyTorch 模型转换成功")

                # 5. 验证转换结果
                print("\n🧪 验证转换结果...")
                success = validate_conversion(mlmodel, pytorch_model, tensor_input.shape)

                if success:
                    # 保存 PyTorch 模型
                    pytorch_path = output_dir / f"{model_name}_converted.pth"
                    torch.save(pytorch_model.state_dict(), pytorch_path)
                    print(f"   ✅ PyTorch 模型已保存: {pytorch_path}")

                    return {
                        'status': 'success',
                        'coreml_path': str(coreml_path),
                        'onnx_path': str(onnx_path),
                        'pytorch_path': str(pytorch_path),
                        'pytorch_model': pytorch_model,
                        'input_shape': tensor_input.shape
                    }
                else:
                    print("   ❌ 转换验证失败")
                    return {'status': 'validation_failed'}

            except Exception as e:
                print(f"   ❌ ONNX → PyTorch 转换失败: {e}")
                return {'status': 'pytorch_conversion_failed', 'error': str(e)}
        else:
            print("\n⚠️ onnx2pytorch 未安装，跳过 PyTorch 转换")
            return {
                'status': 'onnx_only',
                'coreml_path': str(coreml_path),
                'onnx_path': str(onnx_path),
                'message': '请安装 onnx2pytorch 以完成 PyTorch 转换'
            }

    except Exception as e:
        print(f"❌ 转换失败: {e}")
        return {'status': 'failed', 'error': str(e)}

def validate_conversion(coreml_model, pytorch_model, input_shape):
    """验证转换后的模型输出是否与原始 Core ML 模型一致"""
    try:
        # 创建测试输入
        test_input = np.random.randn(*input_shape).astype(np.float32)

        # Core ML 推理
        coreml_input = {coreml_model.get_spec().description.input[0].name: test_input}
        coreml_output = coreml_model.predict(coreml_input)
        coreml_result = list(coreml_output.values())[0]

        # PyTorch 推理
        pytorch_input = torch.from_numpy(test_input)
        with torch.no_grad():
            pytorch_result = pytorch_model(pytorch_input).numpy()

        # 比较结果
        if coreml_result.shape != pytorch_result.shape:
            print(f"   ⚠️ 输出形状不匹配: Core ML {coreml_result.shape} vs PyTorch {pytorch_result.shape}")
            return False

        # 计算数值差异
        max_diff = np.max(np.abs(coreml_result - pytorch_result))
        mean_diff = np.mean(np.abs(coreml_result - pytorch_result))

        print(f"   📊 数值对齐检查:")
        print(f"      最大差异: {max_diff:.6f}")
        print(f"      平均差异: {mean_diff:.6f}")

        # 判断是否对齐（阈值可以根据需要调整）
        if max_diff < 1e-4:
            print("   ✅ 数值对齐良好 (< 1e-4)")
            return True
        elif max_diff < 1e-3:
            print("   ⚠️ 数值对齐可接受 (< 1e-3)")
            return True
        else:
            print("   ❌ 数值对齐较差 (>= 1e-3)")
            return False

    except Exception as e:
        print(f"   ❌ 验证过程出错: {e}")
        return False

def create_pytorch_model_from_coreml(arch_info):
    """根据Core ML架构信息创建PyTorch模型"""
    
    class RebuiltDeepInfantModel(nn.Module):
        def __init__(self, arch_info):
            super(RebuiltDeepInfantModel, self).__init__()
            self.arch_info = arch_info
            
            # 根据分析的架构信息构建模型
            # 这里需要根据实际的Core ML模型结构进行调整
            
            # 假设是基于VGGish的架构
            if 'VGGish' in str(arch_info):
                self.features = self._make_vggish_features()
                self.classifier = self._make_vggish_classifier(arch_info.get('num_classes', 9))
            else:
                # 使用原始的DeepInfant架构作为后备
                self.features = self._make_default_features()
                self.classifier = self._make_default_classifier(arch_info.get('num_classes', 9))
        
        def _make_vggish_features(self):
            """创建VGGish风格的特征提取层"""
            return nn.Sequential(
                # Block 1
                nn.Conv2d(1, 64, kernel_size=3, padding=1),
                nn.ReLU(inplace=True),
                nn.MaxPool2d(kernel_size=2, stride=2),
                
                # Block 2
                nn.Conv2d(64, 128, kernel_size=3, padding=1),
                nn.ReLU(inplace=True),
                nn.MaxPool2d(kernel_size=2, stride=2),
                
                # Block 3
                nn.Conv2d(128, 256, kernel_size=3, padding=1),
                nn.ReLU(inplace=True),
                nn.Conv2d(256, 256, kernel_size=3, padding=1),
                nn.ReLU(inplace=True),
                nn.MaxPool2d(kernel_size=2, stride=2),
                
                # Block 4
                nn.Conv2d(256, 512, kernel_size=3, padding=1),
                nn.ReLU(inplace=True),
                nn.Conv2d(512, 512, kernel_size=3, padding=1),
                nn.ReLU(inplace=True),
                nn.MaxPool2d(kernel_size=2, stride=2),
            )
        
        def _make_vggish_classifier(self, num_classes):
            """创建VGGish风格的分类器"""
            # 确保num_classes是有效的整数
            if num_classes is None or num_classes <= 0:
                num_classes = 9  # 默认类别数

            return nn.Sequential(
                nn.AdaptiveAvgPool2d((1, 1)),
                nn.Flatten(),
                nn.Linear(512, 4096),
                nn.ReLU(inplace=True),
                nn.Dropout(0.5),
                nn.Linear(4096, 4096),
                nn.ReLU(inplace=True),
                nn.Dropout(0.5),
                nn.Linear(4096, num_classes)
            )
        
        def _make_default_features(self):
            """创建默认的特征提取层"""
            return nn.Sequential(
                nn.Conv2d(1, 64, kernel_size=3, padding=1),
                nn.BatchNorm2d(64),
                nn.ReLU(),
                nn.MaxPool2d(2),
                
                nn.Conv2d(64, 128, kernel_size=3, padding=1),
                nn.BatchNorm2d(128),
                nn.ReLU(),
                nn.MaxPool2d(2),
                
                nn.Conv2d(128, 256, kernel_size=3, padding=1),
                nn.BatchNorm2d(256),
                nn.ReLU(),
                nn.MaxPool2d(2),
            )
        
        def _make_default_classifier(self, num_classes):
            """创建默认的分类器"""
            # 确保num_classes是有效的整数
            if num_classes is None or num_classes <= 0:
                num_classes = 9  # 默认类别数

            return nn.Sequential(
                nn.AdaptiveAvgPool2d((1, 1)),
                nn.Flatten(),
                nn.Linear(256, 512),
                nn.ReLU(),
                nn.Dropout(0.3),
                nn.Linear(512, num_classes)
            )
        
        def forward(self, x):
            x = self.features(x)
            x = self.classifier(x)
            return x
    
    return RebuiltDeepInfantModel(arch_info)

def main():
    print("🚀 Core ML 到 PyTorch 转换工具")
    print("支持两种转换方法:")
    print("1. 最佳实践: Core ML → ONNX → PyTorch (推荐，保留权重)")
    print("2. 传统方法: 架构分析 + 手动重建 (仅架构，无权重)")
    print("=" * 80)

    # 要转换的 Core ML 模型
    models_to_convert = [
        "Models/DeepInfant_VGGish.mlmodel",
        "Models/DeepInfant_AFP.mlmodel",
        "iOS_iPadOS/deepinfant/model/DeepInfant_VGGish.mlmodel",
        "iOS_iPadOS/deepinfant/model/DeepInfant_AFP.mlmodel"
    ]

    # 找到存在的模型文件
    existing_models = [model for model in models_to_convert if Path(model).exists()]

    if not existing_models:
        print("❌ 未找到任何 Core ML 模型文件")
        print("请确保以下路径中至少有一个模型文件:")
        for model in models_to_convert:
            print(f"   - {model}")
        return

    print(f"📁 找到 {len(existing_models)} 个模型文件:")
    for model in existing_models:
        print(f"   - {model}")

    # 创建输出目录
    output_dir = Path("converted_models")
    output_dir.mkdir(exist_ok=True)

    conversion_results = []

    for model_path in existing_models:
        print(f"\n{'='*80}")
        print(f"🔄 处理模型: {model_path}")

        # 方法1: 最佳实践转换 (Core ML → ONNX → PyTorch)
        print(f"\n📋 方法1: 最佳实践转换")
        best_practice_result = convert_coreml_to_onnx_to_pytorch(model_path, output_dir)
        conversion_results.append({
            'model_path': model_path,
            'method': 'best_practice',
            'result': best_practice_result
        })

        # 方法2: 传统架构分析方法 (作为备选)
        print(f"\n📋 方法2: 传统架构分析")
        try:
            analyzer = CoreMLAnalyzer(model_path)
            arch_info = analyzer.analyze_model()

            # 保存架构信息
            arch_file = output_dir / f"{Path(model_path).stem}_architecture.json"
            with open(arch_file, 'w', encoding='utf-8') as f:
                json.dump(arch_info, f, indent=2, ensure_ascii=False)
            print(f"   ✅ 架构信息已保存: {arch_file}")

            # 创建PyTorch模型 (无权重)
            pytorch_model = create_pytorch_model_from_coreml(arch_info)
            test_input = torch.randn(1, 1, 80, 432)

            try:
                output = pytorch_model(test_input)
                print(f"   ✅ PyTorch模型测试成功，输出形状: {output.shape}")

                # 导出为ONNX
                onnx_path = output_dir / f"{Path(model_path).stem}_manual_rebuilt.onnx"
                pytorch_model.eval()
                torch.onnx.export(
                    pytorch_model,
                    test_input,
                    str(onnx_path),
                    export_params=True,
                    opset_version=12,
                    do_constant_folding=True,
                    input_names=['input'],
                    output_names=['output'],
                    dynamic_axes={
                        'input': {0: 'batch_size'},
                        'output': {0: 'batch_size'}
                    }
                )
                print(f"   ✅ ONNX模型已导出: {onnx_path}")

                conversion_results.append({
                    'model_path': model_path,
                    'method': 'manual_rebuild',
                    'result': {
                        'status': 'success',
                        'onnx_path': str(onnx_path),
                        'arch_file': str(arch_file)
                    }
                })

            except Exception as e:
                print(f"   ❌ 模型测试失败: {e}")
                conversion_results.append({
                    'model_path': model_path,
                    'method': 'manual_rebuild',
                    'result': {'status': 'failed', 'error': str(e)}
                })

        except Exception as e:
            print(f"   ❌ 架构分析失败: {e}")
            conversion_results.append({
                'model_path': model_path,
                'method': 'manual_rebuild',
                'result': {'status': 'failed', 'error': str(e)}
            })

    # 保存转换结果报告
    report_path = output_dir / "conversion_report.json"
    with open(report_path, 'w', encoding='utf-8') as f:
        json.dump(conversion_results, f, indent=2, ensure_ascii=False)

    # 显示总结
    print(f"\n{'='*80}")
    print("📊 转换总结")
    print("=" * 80)

    for result in conversion_results:
        model_name = Path(result['model_path']).stem
        method = result['method']
        status = result['result'].get('status', 'unknown')

        if method == 'best_practice':
            method_name = "最佳实践"
        else:
            method_name = "架构分析"

        if status == 'success':
            print(f"✅ {model_name} - {method_name}: 成功")
        elif status == 'onnx_only':
            print(f"⚠️ {model_name} - {method_name}: 仅ONNX转换成功")
        else:
            print(f"❌ {model_name} - {method_name}: 失败")

    print(f"\n📋 详细报告已保存: {report_path}")
    print(f"📁 输出目录: {output_dir}")

    # 安装建议
    if not ONNX2PYTORCH_AVAILABLE:
        print(f"\n💡 建议安装以下依赖以获得最佳转换效果:")
        print("   pip install onnx2pytorch onnx-simplifier")

if __name__ == "__main__":
    main()
