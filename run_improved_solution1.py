#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
运行改进的方案1 - 一键执行训练和测试
================================================================================
自动执行改进的方案1训练，并与原方案对比性能提升
================================================================================
"""

import subprocess
import sys
import time
from pathlib import Path
import json

def check_dependencies():
    """检查依赖包"""
    required_packages = [
        'torch', 'tensorflow', 'tensorflow_hub', 'librosa', 
        'soundfile', 'scikit-learn', 'matplotlib', 'seaborn'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ 缺少依赖包: {', '.join(missing_packages)}")
        print("请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def run_training():
    """运行改进训练"""
    print("🚀 开始改进的方案1训练...")
    print("=" * 80)
    
    try:
        # 运行训练脚本
        result = subprocess.run([
            sys.executable, "improved_solution1_training.py"
        ], capture_output=True, text=True, timeout=7200)  # 2小时超时
        
        if result.returncode == 0:
            print("✅ 训练完成！")
            print(result.stdout)
            return True
        else:
            print("❌ 训练失败！")
            print("错误信息:", result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("⏰ 训练超时（2小时）")
        return False
    except Exception as e:
        print(f"❌ 训练过程中出现错误: {e}")
        return False

def run_testing():
    """运行测试评估"""
    print("\n🧪 开始测试评估...")
    print("=" * 80)
    
    try:
        # 运行测试脚本
        result = subprocess.run([
            sys.executable, "test_improved_solution1.py"
        ], capture_output=True, text=True, timeout=1800)  # 30分钟超时
        
        if result.returncode == 0:
            print("✅ 测试完成！")
            print(result.stdout)
            return True
        else:
            print("❌ 测试失败！")
            print("错误信息:", result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("⏰ 测试超时（30分钟）")
        return False
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        return False

def show_summary():
    """显示结果总结"""
    print("\n📊 改进效果总结")
    print("=" * 80)
    
    # 检查结果文件
    result_file = Path("improved_solution1_results/improved_solution1_evaluation.json")
    if result_file.exists():
        try:
            with open(result_file, 'r', encoding='utf-8') as f:
                results = json.load(f)
            
            print(f"🎯 总体性能:")
            print(f"  准确率: {results['overall_accuracy']:.1%}")
            print(f"  测试样本: {results['total_samples']}")
            print(f"  推理时间: {results['avg_inference_time']:.4f}s")
            
            print(f"\n📈 各类别准确率:")
            class_names_cn = {
                'belly_pain': '腹痛',
                'burping': '打嗝', 
                'discomfort': '不适',
                'hungry': '饥饿',
                'tired': '疲倦'
            }
            
            for class_name, acc in results['class_accuracies'].items():
                cn_name = class_names_cn.get(class_name, class_name)
                improvement = results['improvements'].get(class_name, 0)
                print(f"  {cn_name}: {acc:.1%} ({improvement:+.1%})")
            
            # 重点关注弱势类别
            print(f"\n🎯 弱势类别改进效果:")
            weak_classes = ['discomfort', 'tired']
            for class_name in weak_classes:
                if class_name in results['class_accuracies']:
                    cn_name = class_names_cn[class_name]
                    acc = results['class_accuracies'][class_name]
                    improvement = results['improvements'].get(class_name, 0)
                    print(f"  {cn_name}: {acc:.1%} (提升 {improvement:+.1%})")
            
            # 判断是否达到目标
            target_accuracy = 0.85  # 85%目标
            if results['overall_accuracy'] >= target_accuracy:
                print(f"\n🎉 恭喜！已达到85%目标准确率！")
            else:
                gap = target_accuracy - results['overall_accuracy']
                print(f"\n📈 距离85%目标还差 {gap:.1%}")
                
        except Exception as e:
            print(f"❌ 读取结果文件失败: {e}")
    else:
        print("❌ 未找到结果文件")

def main():
    """主函数"""
    print("🚀 改进的方案1 - 一键运行")
    print("=" * 80)
    print("目标：提升不适和疲倦类别识别准确率，保持整体均衡性")
    print("预期：总体准确率从74.6%提升到85%+")
    print("=" * 80)
    
    # 检查依赖
    if not check_dependencies():
        return
    
    # 检查数据目录
    data_dir = Path("Data")
    if not data_dir.exists():
        print("❌ 未找到数据目录 'Data'，请确保数据已准备好")
        return
    
    start_time = time.time()
    
    # 步骤1：运行训练
    if not run_training():
        print("❌ 训练失败，停止执行")
        return
    
    # 步骤2：运行测试
    if not run_testing():
        print("❌ 测试失败，但训练已完成")
        return
    
    # 步骤3：显示总结
    show_summary()
    
    # 计算总耗时
    total_time = time.time() - start_time
    hours = int(total_time // 3600)
    minutes = int((total_time % 3600) // 60)
    seconds = int(total_time % 60)
    
    print(f"\n⏱️ 总耗时: {hours}小时{minutes}分钟{seconds}秒")
    print(f"\n✅ 改进的方案1执行完成！")
    
    # 提供后续建议
    print(f"\n💡 后续建议:")
    print(f"  1. 查看生成的对比图表: improved_solution1_comparison.png")
    print(f"  2. 检查详细结果: improved_solution1_results/")
    print(f"  3. 如果效果不理想，可以调整超参数重新训练")
    print(f"  4. 考虑与方案2集成学习结合使用")

if __name__ == "__main__":
    main()
