#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试混合方案模型
================================================================================
测试结合方案1和方案2优势的混合模型性能
================================================================================
"""

import torch
import torch.nn as nn
from torch.utils.data import DataLoader
import numpy as np
from pathlib import Path
import json
import time
from tqdm import tqdm
from sklearn.metrics import classification_report, confusion_matrix
import matplotlib.pyplot as plt
import seaborn as sns

# 导入混合方案模块
from hybrid_solution_v1_v2 import HybridDeepInfantModel, HybridFeatureExtractor, HybridInfantCryDataset

class HybridPredictor:
    """混合方案预测器"""
    
    def __init__(self, model_path, device='cuda'):
        self.device = torch.device(device if torch.cuda.is_available() else 'cpu')
        self.feature_extractor = HybridFeatureExtractor()
        
        # 加载模型
        self.model = torch.load(model_path, map_location=self.device)
        self.model.eval()
        
        self.label_map = {
            0: 'belly_pain',
            1: 'burping',
            2: 'discomfort', 
            3: 'hungry',
            4: 'tired'
        }
        
        self.label_names_cn = {
            'belly_pain': '腹痛',
            'burping': '打嗝',
            'discomfort': '不适',
            'hungry': '饥饿',
            'tired': '疲倦'
        }
    
    def predict_single(self, audio_file):
        """预测单个音频文件"""
        try:
            start_time = time.time()
            
            # 提取特征
            features = self.feature_extractor.extract_features(audio_file)
            if features is None:
                return {'error': '特征提取失败'}
            
            # 预测
            features_tensor = torch.FloatTensor(features).unsqueeze(0).to(self.device)
            
            with torch.no_grad():
                outputs = self.model(features_tensor)
                probabilities = torch.softmax(outputs, dim=1)
                confidence, predicted = torch.max(probabilities, 1)
            
            predicted_idx = predicted.item()
            predicted_name = self.label_map[predicted_idx]
            predicted_cn = self.label_names_cn[predicted_name]
            confidence_score = confidence.item()
            
            inference_time = time.time() - start_time
            
            return {
                'filename': Path(audio_file).name,
                'predicted_idx': predicted_idx,
                'predicted_name': predicted_name,
                'predicted_cn': predicted_cn,
                'confidence': confidence_score,
                'inference_time': inference_time,
                'probabilities': probabilities.cpu().numpy().tolist()[0]
            }
            
        except Exception as e:
            return {'error': f'预测失败: {str(e)}'}
    
    def predict_batch(self, audio_dir):
        """批量预测目录中的音频文件"""
        audio_dir = Path(audio_dir)
        audio_files = list(audio_dir.glob('*.wav'))
        
        results = []
        for audio_file in tqdm(audio_files, desc=f'预测 {audio_dir.name}'):
            result = self.predict_single(audio_file)
            results.append(result)
        
        return results

def evaluate_hybrid_model_detailed(model_path, data_dir, device='cuda'):
    """详细评估混合模型"""
    print("🧪 详细评估混合方案模型")
    print("=" * 60)
    
    # 创建预测器
    predictor = HybridPredictor(model_path, device)
    
    # 测试目录
    test_dirs = [
        'Data/belly_pain',
        'Data/burping',
        'Data/discomfort', 
        'Data/hungry',
        'Data/tired'
    ]
    
    all_results = []
    
    print(f"\n{'='*60}")
    print("🔄 开始详细测试...")
    
    for test_dir in test_dirs:
        if Path(test_dir).exists():
            print(f"\n📁 测试目录: {test_dir}")
            results = predictor.predict_batch(test_dir)
            
            # 为每个结果添加目录信息
            dir_name = Path(test_dir).name
            for result in results:
                result['directory'] = dir_name
            
            all_results.extend(results)
            
            # 显示一些示例结果
            valid_results = [r for r in results if 'error' not in r]
            if valid_results:
                print(f"   处理了 {len(valid_results)} 个文件")
                for i, result in enumerate(valid_results[:3]):
                    print(f"   示例 {i+1}: {result['filename']} -> {result['predicted_cn']} "
                          f"(置信度: {result['confidence']:.3f})")
            else:
                print(f"   ⚠️ 该目录中没有有效的预测结果")
    
    # 计算详细性能指标
    if all_results:
        print(f"\n📊 详细性能分析:")
        
        # 按目录分别计算准确率
        dir_accuracies = {}
        total_correct = 0
        total_samples = 0
        
        # 类别映射
        dir_to_label = {
            'belly_pain': 0,
            'burping': 1,
            'discomfort': 2,
            'hungry': 3,
            'tired': 4
        }
        
        for test_dir in test_dirs:
            dir_name = Path(test_dir).name
            if Path(test_dir).exists():
                dir_results = [r for r in all_results if r.get('directory') == dir_name and 'error' not in r]
                
                if dir_results:
                    expected_label = dir_to_label[dir_name]
                    correct_predictions = sum(1 for r in dir_results if r['predicted_idx'] == expected_label)
                    total_predictions = len(dir_results)
                    accuracy = correct_predictions / total_predictions if total_predictions > 0 else 0
                    
                    dir_accuracies[dir_name] = {
                        'accuracy': accuracy,
                        'correct': correct_predictions,
                        'total': total_predictions,
                        'samples': total_predictions
                    }
                    
                    print(f"   {dir_name}: {accuracy:.3f} ({correct_predictions}/{total_predictions})")
                    
                    total_correct += correct_predictions
                    total_samples += total_predictions
        
        # 计算总体准确率
        overall_accuracy = total_correct / total_samples if total_samples > 0 else 0
        
        # 计算平均推理时间
        inference_times = [r['inference_time'] for r in all_results if 'inference_time' in r]
        avg_inference_time = np.mean(inference_times) if inference_times else 0
        
        print(f"\n🎯 混合方案总体性能:")
        print(f"   总体准确率: {overall_accuracy:.3f} ({overall_accuracy*100:.1f}%)")
        print(f"   总样本数: {total_samples}")
        print(f"   平均推理时间: {avg_inference_time:.3f}s")
        
        # 与现有方案对比
        print(f"\n📈 与现有方案性能对比:")
        print(f"   原始模型准确率: 13.1%")
        print(f"   简化训练准确率: 38.9%")
        print(f"   方案1准确率: 74.6%")
        print(f"   方案2准确率: 90.2%")
        print(f"   混合方案准确率: {overall_accuracy*100:.1f}%")
        
        improvement_from_original = (overall_accuracy * 100) - 13.1
        improvement_from_simple = (overall_accuracy * 100) - 38.9
        improvement_from_v1 = (overall_accuracy * 100) - 74.6
        improvement_from_v2 = (overall_accuracy * 100) - 90.2
        
        print(f"   相比原始提升: {improvement_from_original:+.1f} 个百分点")
        print(f"   相比简化训练提升: {improvement_from_simple:+.1f} 个百分点")
        print(f"   相比方案1提升: {improvement_from_v1:+.1f} 个百分点")
        print(f"   相比方案2提升: {improvement_from_v2:+.1f} 个百分点")
        
        # 分析类别均衡性
        category_accs = [data['accuracy'] * 100 for data in dir_accuracies.values()]
        std_dev = np.std(category_accs)
        print(f"\n🎯 类别均衡性分析:")
        print(f"   类别间标准差: {std_dev:.1f}% (越小越均衡)")
        print(f"   最高类别准确率: {max(category_accs):.1f}%")
        print(f"   最低类别准确率: {min(category_accs):.1f}%")
        print(f"   准确率差距: {max(category_accs) - min(category_accs):.1f}%")
        
        # 保存详细结果
        results_summary = {
            'method': 'Hybrid Solution (V1+V2)',
            'overall_accuracy': overall_accuracy,
            'total_samples': total_samples,
            'avg_inference_time': avg_inference_time,
            'dir_accuracies': dir_accuracies,
            'improvement_over_original': improvement_from_original,
            'improvement_over_simple': improvement_from_simple,
            'improvement_over_v1': improvement_from_v1,
            'improvement_over_v2': improvement_from_v2,
            'category_balance_std': std_dev,
            'detailed_results': all_results
        }
        
        report_path = Path("hybrid_solution_performance_report.json")
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(results_summary, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"\n📋 详细性能报告已保存: {report_path}")
        
        return results_summary
    else:
        print("❌ 没有获得任何测试结果")
        return None

def main():
    print("🚀 混合方案性能测试")
    print("=" * 60)
    print("测试结合方案1和方案2优势的混合模型")
    print("=" * 60)
    
    # 模型路径
    model_path = "hybrid_solution_results/hybrid_full_model.pth"
    
    if not Path(model_path).exists():
        print(f"❌ 模型文件不存在: {model_path}")
        print("请先运行 python hybrid_solution_v1_v2.py 训练模型")
        return
    
    # 运行详细测试
    results = evaluate_hybrid_model_detailed(model_path, "Data")
    
    # 显示最终总结
    if results:
        print(f"\n{'='*60}")
        print("🏆 混合方案最终性能总结")
        print("=" * 60)
        
        print(f"📊 混合方案模型:")
        print(f"   总体准确率: {results['overall_accuracy']:.3f} ({results['overall_accuracy']*100:.1f}%)")
        print(f"   测试样本数: {results['total_samples']}")
        print(f"   平均推理时间: {results['avg_inference_time']:.3f}s")
        print(f"   类别均衡性: {results['category_balance_std']:.1f}% 标准差")
        
        print(f"\n   各类别准确率:")
        for dir_name, dir_result in results['dir_accuracies'].items():
            print(f"     {dir_name}: {dir_result['accuracy']:.3f} ({dir_result['samples']} 样本)")
        
        print(f"\n🎉 混合方案测试完成！")
        
        # 判断是否达到目标
        target_accuracy = 0.89  # 89%目标
        if results['overall_accuracy'] >= target_accuracy:
            print(f"🎉 恭喜！混合方案已达到89%目标准确率！")
        else:
            gap = target_accuracy - results['overall_accuracy']
            print(f"📈 距离89%目标还差 {gap:.1%}")
    else:
        print("❌ 测试失败")

if __name__ == "__main__":
    main()
